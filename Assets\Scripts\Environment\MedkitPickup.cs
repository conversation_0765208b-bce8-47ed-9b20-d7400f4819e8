using UnityEngine;
using System.Collections;

/// <summary>
/// Medkit pickup system for health management training
/// </summary>
public class MedkitPickup : MonoBehaviour
{
    [Header("💊 Medkit Settings")]
    public float healAmount = 50f;
    public float pickupRange = 2f;
    public bool autoPickup = true;
    public float respawnTime = 45f;

    [Header("🎯 Usage Settings")]
    public bool instantHeal = true;
    public float healOverTime = 0f; // If > 0, heal over time instead of instant
    public float healDuration = 5f;

    // Visual effects
    private bool isPickedUp = false;
    private Renderer medkitRenderer;
    private Collider medkitCollider;
    private ParticleSystem healEffect;

    void Start()
    {
        medkitRenderer = GetComponent<Renderer>();
        medkitCollider = GetComponent<Collider>();

        // Create heal effect particles
        CreateHealEffect();

        // Add floating animation
        StartCoroutine(FloatingAnimation());

        Debug.Log($"💊 Medkit spawned - Heal: {healAmount} HP");
    }

    void CreateHealEffect()
    {
        GameObject effectObj = new GameObject("HealEffect");
        effectObj.transform.SetParent(transform);
        effectObj.transform.localPosition = Vector3.zero;

        healEffect = effectObj.AddComponent<ParticleSystem>();
        var main = healEffect.main;
        main.startColor = Color.green;
        main.startSize = 0.1f;
        main.startSpeed = 2f;
        main.maxParticles = 20;

        var emission = healEffect.emission;
        emission.rateOverTime = 10f;

        var shape = healEffect.shape;
        shape.shapeType = ParticleSystemShapeType.Sphere;
        shape.radius = 0.5f;
    }

    IEnumerator FloatingAnimation()
    {
        Vector3 startPos = transform.position;

        while (!isPickedUp)
        {
            float newY = startPos.y + Mathf.Sin(Time.time * 1.5f) * 0.15f;
            transform.position = new Vector3(startPos.x, newY, startPos.z);
            transform.Rotate(0, 30f * Time.deltaTime, 0);
            yield return null;
        }
    }

    void OnTriggerEnter(Collider other)
    {
        if (isPickedUp) return;

        // Check if player or squadmate can use the medkit
        if (other.CompareTag("Player") || other.GetComponent<SquadMateAgent>() != null)
        {
            HealthSystem healthSystem = other.GetComponent<HealthSystem>();
            if (healthSystem == null)
            {
                healthSystem = other.gameObject.AddComponent<HealthSystem>();
            }

            // Only pickup if health is not full
            if (healthSystem.GetHealthPercent() < 1f)
            {
                PickupMedkit(other.gameObject, healthSystem);
            }
        }
    }

    void PickupMedkit(GameObject picker, HealthSystem healthSystem)
    {
        if (isPickedUp) return;

        isPickedUp = true;

        Debug.Log($"💊 {picker.name} picked up medkit!");

        // Apply healing
        if (instantHeal)
        {
            healthSystem.Heal(healAmount);
        }
        else
        {
            StartCoroutine(HealOverTime(healthSystem));
        }

        // Reward agent for smart medkit usage
        SquadMateAgent agent = picker.GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            float healthPercent = healthSystem.GetHealthPercent();

            // Reward based on how low health was (smart usage)
            float smartUsageReward = (1f - healthPercent) * 0.4f;
            agent.AddReward(0.2f + smartUsageReward);

            Debug.Log($"🎯 Agent rewarded {0.2f + smartUsageReward:F2} for medkit usage at {healthPercent:P0} health");
        }

        // Visual pickup effect
        StartCoroutine(PickupEffect());
    }

    IEnumerator HealOverTime(HealthSystem healthSystem)
    {
        float healPerSecond = healAmount / healDuration;
        float timer = 0f;

        while (timer < healDuration && healthSystem.GetHealthPercent() < 1f)
        {
            healthSystem.Heal(healPerSecond * Time.deltaTime);
            timer += Time.deltaTime;
            yield return null;
        }
    }

    IEnumerator PickupEffect()
    {
        // Burst heal effect
        var emission = healEffect.emission;
        emission.SetBursts(new ParticleSystem.Burst[]
        {
            new ParticleSystem.Burst(0f, 50)
        });

        // Scale up and fade out
        Vector3 originalScale = transform.localScale;
        Color originalColor = medkitRenderer.material.color;

        float timer = 0f;
        while (timer < 0.8f)
        {
            timer += Time.deltaTime * 1.5f;

            // Scale up
            transform.localScale = Vector3.Lerp(originalScale, originalScale * 1.3f, timer);

            // Fade out
            Color newColor = originalColor;
            newColor.a = 1f - timer;
            medkitRenderer.material.color = newColor;

            yield return null;
        }

        // Hide the medkit
        medkitRenderer.enabled = false;
        medkitCollider.enabled = false;
        healEffect.Stop();

        // Respawn after delay
        if (respawnTime > 0)
        {
            yield return new WaitForSeconds(respawnTime);
            RespawnMedkit();
        }
        else
        {
            Destroy(gameObject);
        }
    }

    void RespawnMedkit()
    {
        isPickedUp = false;
        medkitRenderer.enabled = true;
        medkitCollider.enabled = true;
        healEffect.Play();

        // Reset visual properties
        transform.localScale = Vector3.one * 0.7f;
        Color color = medkitRenderer.material.color;
        color.a = 1f;
        medkitRenderer.material.color = color;

        // Respawn effect
        StartCoroutine(RespawnEffect());
        StartCoroutine(FloatingAnimation());

        Debug.Log($"🔄 Medkit respawned!");
    }

    IEnumerator RespawnEffect()
    {
        // Spawn with a green flash
        for (int i = 0; i < 3; i++)
        {
            medkitRenderer.material.color = Color.white;
            yield return new WaitForSeconds(0.1f);
            medkitRenderer.material.color = Color.green;
            yield return new WaitForSeconds(0.1f);
        }
    }

    void OnDrawGizmosSelected()
    {
        // Draw pickup range
        Gizmos.color = Color.green;
        Gizmos.DrawWireSphere(transform.position, pickupRange);
    }
}

/// <summary>
/// Health system for entities that can take damage and heal
/// </summary>
public class HealthSystem : MonoBehaviour
{
    [Header("❤️ Health Settings")]
    public float maxHealth = 100f;
    public float currentHealth = 100f;
    public bool canRegenerate = false;
    public float regenRate = 5f; // HP per second
    public float regenDelay = 5f; // Delay after taking damage

    [Header("🎯 Combat Stats")]
    public float totalDamageTaken = 0f;
    public float totalHealingReceived = 0f;
    public int timesHealed = 0;
    public int timesDied = 0;

    private float lastDamageTime = 0f;
    private SquadMateAgent agent;

    void Start()
    {
        currentHealth = maxHealth;
        agent = GetComponent<SquadMateAgent>();
    }

    void Update()
    {
        // Natural regeneration
        if (canRegenerate && currentHealth < maxHealth && Time.time - lastDamageTime > regenDelay)
        {
            Heal(regenRate * Time.deltaTime);
        }
    }

    public void TakeDamage(float damage)
    {
        currentHealth -= damage;
        totalDamageTaken += damage;
        lastDamageTime = Time.time;

        Debug.Log($"💔 {gameObject.name} takes {damage} damage! Health: {currentHealth}/{maxHealth}");

        // Penalty for taking damage
        if (agent != null)
        {
            agent.AddReward(-0.1f);
        }

        if (currentHealth <= 0)
        {
            Die();
        }
    }

    public void Heal(float healAmount)
    {
        float actualHeal = Mathf.Min(healAmount, maxHealth - currentHealth);
        currentHealth += actualHeal;
        totalHealingReceived += actualHeal;

        if (actualHeal > 0)
        {
            timesHealed++;
            Debug.Log($"💚 {gameObject.name} healed for {actualHeal} HP! Health: {currentHealth}/{maxHealth}");
        }

        // Clamp to max health
        currentHealth = Mathf.Min(currentHealth, maxHealth);
    }

    public float GetHealthPercent()
    {
        return currentHealth / maxHealth;
    }

    public bool IsLowHealth(float threshold = 0.3f)
    {
        return GetHealthPercent() < threshold;
    }

    public bool IsCriticalHealth(float threshold = 0.15f)
    {
        return GetHealthPercent() < threshold;
    }

    public bool NeedsHealing(float threshold = 0.8f)
    {
        return GetHealthPercent() < threshold;
    }

    void Die()
    {
        timesDied++;
        Debug.Log($"💀 {gameObject.name} died!");

        // Major penalty for dying
        if (agent != null)
        {
            agent.AddReward(-1.0f);
            agent.EndEpisode(); // End episode on death
        }

        // Reset health for respawn
        currentHealth = maxHealth;
    }

    // Visual health indicator
    void OnGUI()
    {
        if (agent == null) return; // Only show for agent

        Vector3 screenPos = Camera.main.WorldToScreenPoint(transform.position + Vector3.up * 2f);
        if (screenPos.z > 0)
        {
            float healthPercent = GetHealthPercent();
            Color healthColor = Color.Lerp(Color.red, Color.green, healthPercent);

            GUI.color = healthColor;
            GUI.Label(new Rect(screenPos.x - 25, Screen.height - screenPos.y - 10, 50, 20),
                     $"HP: {currentHealth:F0}/{maxHealth:F0}");
            GUI.color = Color.white;
        }
    }
}
