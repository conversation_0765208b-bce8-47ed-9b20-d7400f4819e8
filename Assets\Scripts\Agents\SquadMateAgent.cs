using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Unity.MLAgents;
using Unity.MLAgents.Sensors;
using Unity.MLAgents.Actuators;
using Unity.Collections;
using Unity.Jobs;

// Unity 6 Enhanced SquadMate Agent with performance optimizations

public class SquadMateAgent : Agent
{
    [Header("Agent Settings")]
    public float moveSpeed = 5f;
    public float rotationSpeed = 100f;
    public float maxHealth = 100f;
    public float reviveRange = 2f;
    public float followDistance = 3f;

    [Header("Unity 6 Performance Settings")]
    public bool useJobSystem = true;
    public bool enableBurstCompilation = true;
    public int maxObservationDistance = 20;

    [Header("References")]
    public Transform player;
    public Transform[] enemies;
    public Transform[] medkits;
    public Transform[] weapons;
    public GameEnvironment environment;

    [Header("Agent State")]
    public float currentHealth;
    public bool isReviving;
    public bool hasWeapon;
    public AgentState currentState;

    [Header("🎯 PUBG Combat State")]
    public bool isInCombat = false;
    public bool isTakingCover = false;
    public bool isHealing = false;
    public float lastDamageTime = 0f;
    public float combatTimeout = 10f;
    public Vector3 lastKnownEnemyPosition;

    [Header("🎒 PUBG Systems")]
    public PUBGInventory inventory;
    public PUBGArmorSystem armorSystem;
    public PUBGItemSystem itemSystem;

    public enum AgentState
    {
        Following,
        Reviving,
        Fighting,
        Seeking,
        Healing,
        Looting,
        TakingCover,
        Flanking,
        Retreating
    }

    private Rigidbody rb;
    private RewardCalculator rewardCalc;
    private Vector3 lastPlayerPosition;
    private float reviveTimer;
    private float stateTimer;

    public override void Initialize()
    {
        rb = GetComponent<Rigidbody>();
        rewardCalc = GetComponent<RewardCalculator>();
        currentHealth = maxHealth;
        currentState = AgentState.Following;

        // Initialize PUBG systems
        InitializePUBGSystems();
    }

    void InitializePUBGSystems()
    {
        // Get or create PUBG components
        inventory = GetComponent<PUBGInventory>();
        if (inventory == null)
        {
            inventory = gameObject.AddComponent<PUBGInventory>();
        }

        armorSystem = GetComponent<PUBGArmorSystem>();
        if (armorSystem == null)
        {
            armorSystem = gameObject.AddComponent<PUBGArmorSystem>();
        }

        // Find item system in environment
        if (itemSystem == null)
        {
            itemSystem = FindObjectOfType<PUBGItemSystem>();
        }

        // Find environment if not assigned
        if (environment == null)
        {
            environment = FindObjectOfType<GameEnvironment>();
        }

        Debug.Log("🎯 PUBG systems initialized for SquadMate Agent");
    }

    public override void OnEpisodeBegin()
    {
        // Reset agent state
        currentHealth = maxHealth;
        isReviving = false;
        hasWeapon = false;
        currentState = AgentState.Following;
        reviveTimer = 0f;
        stateTimer = 0f;

        // Reset PUBG combat state
        isInCombat = false;
        isTakingCover = false;
        isHealing = false;
        lastDamageTime = 0f;

        // Reset position safely
        Vector3 spawnPosition = GetSafeSpawnPosition();
        transform.position = spawnPosition;
        transform.rotation = Quaternion.identity;

        // Reset physics
        if (rb != null)
        {
            rb.linearVelocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
        }

        // Update references
        UpdateReferences();
    }

    Vector3 GetSafeSpawnPosition()
    {
        // Try to get spawn position from environment
        if (environment != null)
        {
            return environment.GetRandomSpawnPoint();
        }

        // Fallback: spawn at a safe default position
        Vector3 defaultSpawn = new Vector3(
            UnityEngine.Random.Range(-10f, 10f),
            1f,
            UnityEngine.Random.Range(-10f, 10f)
        );

        Debug.LogWarning("⚠️ Environment not found, using default spawn position: " + defaultSpawn);
        return defaultSpawn;
    }

    public override void CollectObservations(VectorSensor sensor)
    {
        // Agent's own state (10 observations)
        sensor.AddObservation(transform.localPosition);
        sensor.AddObservation(transform.forward);
        sensor.AddObservation(currentHealth / maxHealth);
        sensor.AddObservation(hasWeapon ? 1f : 0f);
        sensor.AddObservation((int)currentState);
        sensor.AddObservation(isReviving ? 1f : 0f);

        // PUBG-specific observations (4 observations)
        sensor.AddObservation(isInCombat ? 1f : 0f);
        sensor.AddObservation(isTakingCover ? 1f : 0f);
        sensor.AddObservation(inventory != null ? inventory.GetInventoryFullness() : 0f);
        sensor.AddObservation(armorSystem != null ? armorSystem.GetArmorLevel() / 3f : 0f);

        // Player state (4 observations)
        if (player != null)
        {
            Vector3 playerDirection = (player.position - transform.position).normalized;
            float playerDistance = Vector3.Distance(transform.position, player.position);
            sensor.AddObservation(playerDirection);
            sensor.AddObservation(playerDistance / 20f); // Normalize to reasonable range
        }
        else
        {
            sensor.AddObservation(Vector3.zero);
            sensor.AddObservation(0f);
        }

        // Nearest enemy (4 observations)
        Transform nearestEnemy = GetNearestObject(enemies);
        if (nearestEnemy != null)
        {
            Vector3 enemyDirection = (nearestEnemy.position - transform.position).normalized;
            float enemyDistance = Vector3.Distance(transform.position, nearestEnemy.position);
            sensor.AddObservation(enemyDirection);
            sensor.AddObservation(enemyDistance / 20f);
        }
        else
        {
            sensor.AddObservation(Vector3.zero);
            sensor.AddObservation(0f);
        }

        // Nearest medkit (4 observations)
        Transform nearestMedkit = GetNearestObject(medkits);
        if (nearestMedkit != null)
        {
            Vector3 medkitDirection = (nearestMedkit.position - transform.position).normalized;
            float medkitDistance = Vector3.Distance(transform.position, nearestMedkit.position);
            sensor.AddObservation(medkitDirection);
            sensor.AddObservation(medkitDistance / 20f);
        }
        else
        {
            sensor.AddObservation(Vector3.zero);
            sensor.AddObservation(0f);
        }

        // Nearest weapon (4 observations)
        Transform nearestWeapon = GetNearestObject(weapons);
        if (nearestWeapon != null)
        {
            Vector3 weaponDirection = (nearestWeapon.position - transform.position).normalized;
            float weaponDistance = Vector3.Distance(transform.position, nearestWeapon.position);
            sensor.AddObservation(weaponDirection);
            sensor.AddObservation(weaponDistance / 20f);
        }
        else
        {
            sensor.AddObservation(Vector3.zero);
            sensor.AddObservation(0f);
        }
    }

    public override void OnActionReceived(ActionBuffers actionBuffers)
    {
        // Continuous actions
        float moveX = actionBuffers.ContinuousActions[0];
        float moveZ = actionBuffers.ContinuousActions[1];
        float rotate = actionBuffers.ContinuousActions[2];

        // Discrete actions
        int actionType = actionBuffers.DiscreteActions[0];

        // Movement
        Vector3 movement = new Vector3(moveX, 0, moveZ) * moveSpeed * Time.fixedDeltaTime;
        rb.MovePosition(transform.position + transform.TransformDirection(movement));

        // Rotation
        transform.Rotate(0, rotate * rotationSpeed * Time.fixedDeltaTime, 0);

        // Execute discrete action
        ExecuteAction(actionType);

        // Calculate rewards
        CalculateRewards();

        // Update state
        UpdateState();

        // Check episode end conditions
        CheckEpisodeEnd();
    }

    private void ExecuteAction(int actionType)
    {
        switch (actionType)
        {
            case 0: // Do nothing
                break;
            case 1: // Start reviving
                TryRevive();
                break;
            case 2: // Shoot/Attack
                TryAttack();
                break;
            case 3: // Pick up item / Loot
                TryLoot();
                break;
            case 4: // Use healing item
                TryHeal();
                break;
            case 5: // Take cover
                TryTakeCover();
                break;
            case 6: // Throw grenade
                TryThrowGrenade();
                break;
            case 7: // Reload weapon
                TryReload();
                break;
        }
    }

    private void CalculateRewards()
    {
        if (rewardCalc != null)
        {
            rewardCalc.CalculateFrameRewards(this);
        }

        // Basic survival reward
        AddReward(0.001f);

        // Formation keeping reward
        if (player != null)
        {
            float distanceToPlayer = Vector3.Distance(transform.position, player.position);
            if (distanceToPlayer <= followDistance * 1.5f && distanceToPlayer >= followDistance * 0.5f)
            {
                AddReward(0.01f);
            }
            else if (distanceToPlayer > followDistance * 3f)
            {
                AddReward(-0.005f); // Penalty for being too far
            }
        }

        // Health penalty
        if (currentHealth < maxHealth * 0.3f)
        {
            AddReward(-0.002f);
        }
    }

    private void UpdateState()
    {
        stateTimer += Time.fixedDeltaTime;

        // Update combat status
        UpdateCombatStatus();

        // PUBG-style state transition logic with priorities
        if (isReviving)
        {
            currentState = AgentState.Reviving;
        }
        else if (currentHealth <= 0)
        {
            currentState = AgentState.Retreating; // Dead/downed
        }
        else if (ShouldRetreat())
        {
            currentState = AgentState.Retreating;
        }
        else if (ShouldTakeCover())
        {
            currentState = AgentState.TakingCover;
        }
        else if (ShouldHeal())
        {
            currentState = AgentState.Healing;
        }
        else if (isInCombat)
        {
            currentState = ChooseCombatState();
        }
        else if (ShouldLoot())
        {
            currentState = AgentState.Looting;
        }
        else if (!hasWeapon && GetNearestObject(weapons) != null)
        {
            currentState = AgentState.Seeking;
        }
        else
        {
            currentState = AgentState.Following;
        }
    }

    void UpdateCombatStatus()
    {
        Transform nearestEnemy = GetNearestObject(enemies);

        if (nearestEnemy != null)
        {
            float enemyDistance = Vector3.Distance(transform.position, nearestEnemy.position);

            if (enemyDistance < 15f) // Combat range
            {
                isInCombat = true;
                lastDamageTime = Time.time;
                lastKnownEnemyPosition = nearestEnemy.position;
            }
            else if (Time.time - lastDamageTime > combatTimeout)
            {
                isInCombat = false;
            }
        }
        else if (Time.time - lastDamageTime > combatTimeout)
        {
            isInCombat = false;
        }
    }

    bool ShouldRetreat()
    {
        // Retreat if critically low health and in combat
        return isInCombat && currentHealth < maxHealth * 0.25f;
    }

    bool ShouldTakeCover()
    {
        // Take cover if in combat and low health but not critical
        return isInCombat && currentHealth < maxHealth * 0.5f && !ShouldRetreat();
    }

    bool ShouldHeal()
    {
        // Heal if not in immediate combat and health is low
        return !isInCombat && currentHealth < maxHealth * 0.7f && HasHealingItems();
    }

    bool ShouldLoot()
    {
        // Loot if not in combat and there's valuable loot nearby
        if (isInCombat) return false;

        if (itemSystem != null)
        {
            GameObject bestLoot = itemSystem.GetBestLootForAgent(transform.position, this);
            return bestLoot != null;
        }

        return false;
    }

    AgentState ChooseCombatState()
    {
        Transform nearestEnemy = GetNearestObject(enemies);
        if (nearestEnemy == null) return AgentState.Following;

        float enemyDistance = Vector3.Distance(transform.position, nearestEnemy.position);

        // Close range - fight directly
        if (enemyDistance < 8f)
        {
            return AgentState.Fighting;
        }
        // Medium range - try to flank if possible
        else if (enemyDistance < 12f && CanFlank())
        {
            return AgentState.Flanking;
        }
        // Long range - fight from current position
        else
        {
            return AgentState.Fighting;
        }
    }

    bool HasHealingItems()
    {
        return inventory != null && inventory.GetBestHealingItem(currentHealth / maxHealth) != null;
    }

    bool CanFlank()
    {
        // Simple flanking check - can we move to a different angle?
        return !isTakingCover && hasWeapon;
    }

    private void CheckEpisodeEnd()
    {
        // End episode if agent dies
        if (currentHealth <= 0)
        {
            AddReward(-1f);
            EndEpisode();
        }

        // End episode if player dies (failure)
        if (player != null && player.GetComponent<PlayerController>().currentHealth <= 0)
        {
            AddReward(-0.5f);
            EndEpisode();
        }

        // End episode after time limit
        if (stateTimer > 300f) // 5 minutes
        {
            AddReward(0.1f); // Small bonus for surviving
            EndEpisode();
        }
    }

    private Transform GetNearestObject(Transform[] objects)
    {
        if (objects == null || objects.Length == 0) return null;

        Transform nearest = null;
        float minDistance = float.MaxValue;

        foreach (Transform obj in objects)
        {
            if (obj == null) continue;

            float distance = Vector3.Distance(transform.position, obj.position);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearest = obj;
            }
        }

        return nearest;
    }

    private void TryRevive()
    {
        if (player != null && Vector3.Distance(transform.position, player.position) <= reviveRange)
        {
            PlayerController playerController = player.GetComponent<PlayerController>();
            if (playerController != null && playerController.isDowned)
            {
                isReviving = true;
                reviveTimer += Time.fixedDeltaTime;

                if (reviveTimer >= 3f) // 3 seconds to revive
                {
                    playerController.Revive();
                    AddReward(0.5f);
                    isReviving = false;
                    reviveTimer = 0f;
                }
            }
        }
        else
        {
            isReviving = false;
            reviveTimer = 0f;
        }
    }

    private void TryAttack()
    {
        // Try using weapon system first
        WeaponSystem weaponSystem = GetComponent<WeaponSystem>();
        if (weaponSystem != null && weaponSystem.HasWeapon())
        {
            Transform targetEnemy = GetNearestObject(enemies);
            if (targetEnemy != null)
            {
                bool hit = weaponSystem.TryShoot(targetEnemy.position);
                if (hit)
                {
                    AddReward(0.3f); // Reward for successful hit
                }
                return;
            }
        }

        // Fallback to basic attack
        if (!hasWeapon) return;

        Transform closestEnemy = GetNearestObject(enemies);
        if (closestEnemy != null && Vector3.Distance(transform.position, closestEnemy.position) <= 8f)
        {
            // Try new EnemyAI system first
            EnemyAI enemyAI = closestEnemy.GetComponent<EnemyAI>();
            if (enemyAI != null)
            {
                enemyAI.TakeDamage(25f);
                AddReward(0.1f);

                if (enemyAI.health <= 0)
                {
                    AddReward(0.3f); // Bonus for eliminating enemy
                }
                return;
            }

            // Fallback to old system
            EnemyController enemy = closestEnemy.GetComponent<EnemyController>();
            if (enemy != null)
            {
                enemy.TakeDamage(25f);
                AddReward(0.1f);

                if (enemy.currentHealth <= 0)
                {
                    AddReward(0.3f); // Bonus for eliminating enemy
                }
            }
        }
    }

    private void TryLoot()
    {
        // Use PUBG item system for smart looting
        if (itemSystem != null)
        {
            GameObject bestLoot = itemSystem.GetBestLootForAgent(transform.position, this);
            if (bestLoot != null && Vector3.Distance(transform.position, bestLoot.transform.position) <= 3f)
            {
                // The PUBGLootPickup component will handle the pickup automatically
                return;
            }
        }

        // Fallback to old weapon pickup system
        Transform nearestWeapon = GetNearestObject(weapons);
        if (nearestWeapon != null && Vector3.Distance(transform.position, nearestWeapon.position) <= 2f)
        {
            hasWeapon = true;
            Destroy(nearestWeapon.gameObject);
            AddReward(0.1f);
            return;
        }
    }

    private void TryTakeCover()
    {
        if (!isInCombat) return;

        // Find nearest cover point
        Vector3 coverPosition = FindNearestCover();
        if (coverPosition != Vector3.zero)
        {
            // Move towards cover
            Vector3 direction = (coverPosition - transform.position).normalized;
            rb.MovePosition(transform.position + direction * moveSpeed * Time.fixedDeltaTime);

            isTakingCover = true;
            AddReward(0.05f); // Small reward for tactical behavior
        }
    }

    private void TryThrowGrenade()
    {
        if (inventory == null || !isInCombat) return;

        // Check if we have throwables
        PUBGItem grenade = GetBestThrowable();
        if (grenade != null)
        {
            Transform nearestEnemy = GetNearestObject(enemies);
            if (nearestEnemy != null)
            {
                float distance = Vector3.Distance(transform.position, nearestEnemy.position);
                if (distance <= grenade.throwRange && distance >= 5f) // Don't throw too close
                {
                    ThrowGrenade(grenade, nearestEnemy.position);
                }
            }
        }
    }

    private void TryReload()
    {
        WeaponSystem weaponSystem = GetComponent<WeaponSystem>();
        if (weaponSystem != null && weaponSystem.HasWeapon())
        {
            // Check if weapon needs reloading (implement in WeaponSystem)
            // For now, just add a small reward for tactical thinking
            AddReward(0.02f);
        }
    }

    Vector3 FindNearestCover()
    {
        // Simple cover finding - look for objects that can provide cover
        Collider[] nearbyObjects = Physics.OverlapSphere(transform.position, 10f);
        Vector3 bestCover = Vector3.zero;
        float bestScore = 0f;

        foreach (Collider obj in nearbyObjects)
        {
            if (obj.gameObject != gameObject && obj.bounds.size.y > 1f) // Tall enough for cover
            {
                Vector3 coverPos = obj.bounds.center;
                float distance = Vector3.Distance(transform.position, coverPos);

                // Prefer closer cover
                float score = 10f / (distance + 1f);

                if (score > bestScore)
                {
                    bestScore = score;
                    bestCover = coverPos;
                }
            }
        }

        return bestCover;
    }

    PUBGItem GetBestThrowable()
    {
        if (inventory == null) return null;

        // Prioritize frag grenades in combat
        foreach (InventoryItem item in inventory.items)
        {
            if (item.itemData.itemType == PUBGItemType.Throwable &&
                item.itemData.itemName.Contains("Frag"))
            {
                return item.itemData;
            }
        }

        // Fallback to any throwable
        foreach (InventoryItem item in inventory.items)
        {
            if (item.itemData.itemType == PUBGItemType.Throwable)
            {
                return item.itemData;
            }
        }

        return null;
    }

    void ThrowGrenade(PUBGItem grenade, Vector3 targetPosition)
    {
        if (inventory.RemoveItem(grenade.itemName, 1))
        {
            // Create grenade effect (simplified)
            StartCoroutine(GrenadeEffect(targetPosition, grenade));
            AddReward(0.2f); // Reward for tactical grenade use

            Debug.Log($"💣 Agent threw {grenade.itemName} at {targetPosition}");
        }
    }

    System.Collections.IEnumerator GrenadeEffect(Vector3 targetPosition, PUBGItem grenade)
    {
        yield return new WaitForSeconds(grenade.fuseTime);

        // Check for enemies in explosion radius
        Collider[] hitTargets = Physics.OverlapSphere(targetPosition, grenade.explosionRadius);

        foreach (Collider target in hitTargets)
        {
            EnemyAI enemy = target.GetComponent<EnemyAI>();
            if (enemy != null)
            {
                enemy.TakeDamage(grenade.explosionDamage);
                AddReward(0.3f); // Bonus for successful grenade hit
            }
        }

        // Visual effect (simplified)
        Debug.Log($"💥 Grenade exploded at {targetPosition} - {grenade.explosionDamage} damage, {grenade.explosionRadius}m radius");
    }

    private void TryHeal()
    {
        // Use PUBG healing system for smart healing
        if (inventory != null)
        {
            float healthPercent = currentHealth / maxHealth;
            PUBGItem bestHealing = inventory.GetBestHealingItem(healthPercent);

            if (bestHealing != null)
            {
                // Start healing process
                StartCoroutine(UseHealingItem(bestHealing));
                return;
            }
        }

        // Try using health system first
        HealthSystem healthSystem = GetComponent<HealthSystem>();
        if (healthSystem != null && healthSystem.NeedsHealing())
        {
            Transform targetMedkit = GetNearestObject(medkits);
            if (targetMedkit != null && Vector3.Distance(transform.position, targetMedkit.position) <= 2f)
            {
                // The medkit pickup will handle the healing automatically
                return;
            }
        }

        // Fallback to basic healing
        Transform closestMedkit = GetNearestObject(medkits);
        if (closestMedkit != null && Vector3.Distance(transform.position, closestMedkit.position) <= 2f)
        {
            currentHealth = Mathf.Min(currentHealth + 50f, maxHealth);
            Destroy(closestMedkit.gameObject);
            AddReward(0.2f);
        }
    }

    System.Collections.IEnumerator UseHealingItem(PUBGItem healingItem)
    {
        if (healingItem == null) yield break;

        isHealing = true;
        float healStartTime = Time.time;

        Debug.Log($"💊 Agent started using {healingItem.itemName} ({healingItem.useTime}s)");

        // Wait for use time
        yield return new WaitForSeconds(healingItem.useTime);

        // Check if we're still safe to heal (not interrupted by combat)
        if (isHealing && !isInCombat)
        {
            // Apply healing
            float healAmount = healingItem.healAmount;
            float maxHeal = maxHealth * healingItem.maxHealthPercent;
            float actualHeal = Mathf.Min(healAmount, maxHeal - currentHealth);

            if (actualHeal > 0)
            {
                currentHealth += actualHeal;
                currentHealth = Mathf.Min(currentHealth, maxHealth);

                // Remove item from inventory
                inventory.UseHealingItem(healingItem);

                // Reward based on healing efficiency
                float efficiency = actualHeal / healingItem.healAmount;
                float reward = 0.3f * efficiency;
                AddReward(reward);

                Debug.Log($"💚 Agent healed {actualHeal} HP using {healingItem.itemName}");
            }
        }
        else
        {
            Debug.Log($"❌ Healing interrupted - combat detected");
            AddReward(-0.1f); // Small penalty for interrupted healing
        }

        isHealing = false;
    }

    private void UpdateReferences()
    {
        if (environment != null)
        {
            player = environment.player;
            enemies = environment.GetEnemies();
            medkits = environment.GetMedkits();
            weapons = environment.GetWeapons();
        }
    }

    public override void Heuristic(in ActionBuffers actionsOut)
    {
        // Manual control for testing
        var continuousActionsOut = actionsOut.ContinuousActions;
        var discreteActionsOut = actionsOut.DiscreteActions;

        continuousActionsOut[0] = Input.GetAxis("Horizontal");
        continuousActionsOut[1] = Input.GetAxis("Vertical");
        continuousActionsOut[2] = Input.GetAxis("Mouse X");

        discreteActionsOut[0] = 0; // Default to no action

        if (Input.GetKey(KeyCode.R)) discreteActionsOut[0] = 1; // Revive
        if (Input.GetKey(KeyCode.Space)) discreteActionsOut[0] = 2; // Attack
        if (Input.GetKey(KeyCode.E)) discreteActionsOut[0] = 3; // Pickup
        if (Input.GetKey(KeyCode.H)) discreteActionsOut[0] = 4; // Heal
    }

    public void TakeDamage(float damage, bool isHeadshot = false)
    {
        // Apply armor damage reduction
        float finalDamage = damage;

        if (armorSystem != null)
        {
            float damageReduction = armorSystem.CalculateDamageReduction(isHeadshot);
            finalDamage = damage * (1f - damageReduction);

            // Damage the armor
            armorSystem.TakeDamage(damage, isHeadshot);

            if (damageReduction > 0)
            {
                Debug.Log($"🛡️ Armor absorbed {damage - finalDamage:F1} damage ({damageReduction:P0} reduction)");
            }
        }

        currentHealth -= finalDamage;
        lastDamageTime = Time.time;
        isInCombat = true;

        // Reward/penalty based on damage taken
        float damagePenalty = -0.1f * (finalDamage / maxHealth);
        AddReward(damagePenalty);

        // Extra penalty for taking damage without armor
        if (armorSystem == null || !armorSystem.HasArmor())
        {
            AddReward(-0.05f);
        }

        if (currentHealth <= 0)
        {
            currentHealth = 0;
            AddReward(-1f); // Large penalty for dying
            Debug.Log("💀 Agent died!");
        }
        else if (currentHealth < maxHealth * 0.25f)
        {
            // Critical health - should prioritize healing/retreating
            AddReward(-0.2f);
        }

        Debug.Log($"💥 Agent took {finalDamage:F1} damage (Health: {currentHealth:F1}/{maxHealth})");
    }
}
