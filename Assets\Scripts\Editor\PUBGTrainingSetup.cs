using UnityEngine;
using UnityEditor;
using Unity.MLAgents.Policies;

/// <summary>
/// Complete PUBG-style training environment setup for SquadMate AI
/// </summary>
public class PUBGTrainingSetup : EditorWindow
{
    [MenuItem("SquadMate AI/🎯 Setup PUBG Training Environment")]
    public static void SetupPUBGTraining()
    {
        Debug.Log("🎯 Setting up PUBG Training Environment...");

        // Create the complete training environment
        CreateTrainingEnvironment();

        Debug.Log("✅ PUBG Training Environment setup complete!");
        Debug.Log("🚀 Ready for advanced combat and loot training!");
    }

    static void CreateTrainingEnvironment()
    {
        // Create main environment object
        GameObject environment = new GameObject("PUBG_Training_Environment");
        environment.transform.position = Vector3.zero;

        // Add environment components
        GameEnvironment gameEnv = environment.AddComponent<GameEnvironment>();
        PUBGItemSystem itemSystem = environment.AddComponent<PUBGItemSystem>();
        ObjectSpawner spawner = environment.AddComponent<ObjectSpawner>();

        // Create ground plane
        CreateGroundPlane(environment.transform);

        // Create spawn points and cover
        CreateSpawnPoints(environment.transform);
        CreateCoverPoints(environment.transform);

        // Create player
        GameObject player = CreatePlayer(environment.transform);

        // Create SquadMate agent
        GameObject squadmate = CreateSquadMateAgent(environment.transform);

        // Configure environment references
        ConfigureEnvironment(gameEnv, itemSystem, player, squadmate);

        // Set environment reference on the agent
        SquadMateAgent agentScript = squadmate.GetComponent<SquadMateAgent>();
        if (agentScript != null)
        {
            agentScript.environment = gameEnv;
        }

        // Create initial enemies
        CreateInitialEnemies(environment.transform, gameEnv);

        // Setup loot spawning
        SetupLootSpawning(itemSystem);

        Debug.Log("🏗️ PUBG Training Environment created successfully!");
    }

    static void CreateGroundPlane(Transform parent)
    {
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "Ground";
        ground.transform.SetParent(parent);
        ground.transform.localPosition = Vector3.zero;
        ground.transform.localScale = new Vector3(10f, 1f, 10f);

        // Create ground material
        Material groundMat = new Material(Shader.Find("Standard"));
        groundMat.color = new Color(0.3f, 0.5f, 0.3f); // Dark green
        ground.GetComponent<Renderer>().sharedMaterial = groundMat;

        // Add collider for physics
        ground.GetComponent<Collider>().isTrigger = false;
    }

    static void CreateSpawnPoints(Transform parent)
    {
        GameObject spawnContainer = new GameObject("SpawnPoints");
        spawnContainer.transform.SetParent(parent);

        // Create spawn points in a circle
        int spawnCount = 8;
        float radius = 20f;

        for (int i = 0; i < spawnCount; i++)
        {
            float angle = (360f / spawnCount) * i * Mathf.Deg2Rad;
            Vector3 position = new Vector3(
                Mathf.Cos(angle) * radius,
                1f,
                Mathf.Sin(angle) * radius
            );

            GameObject spawnPoint = new GameObject($"SpawnPoint_{i + 1}");
            spawnPoint.transform.SetParent(spawnContainer.transform);
            spawnPoint.transform.localPosition = position;

            // Visual indicator
            GameObject indicator = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            indicator.transform.SetParent(spawnPoint.transform);
            indicator.transform.localPosition = Vector3.zero;
            indicator.transform.localScale = new Vector3(0.5f, 0.1f, 0.5f);

            // Create material properly for editor mode
            Material indicatorMat = new Material(Shader.Find("Standard"));
            indicatorMat.color = Color.blue;
            indicator.GetComponent<Renderer>().sharedMaterial = indicatorMat;

            DestroyImmediate(indicator.GetComponent<Collider>());
        }
    }

    static void CreateCoverPoints(Transform parent)
    {
        GameObject coverContainer = new GameObject("CoverPoints");
        coverContainer.transform.SetParent(parent);

        // Create cover objects scattered around
        int coverCount = 12;

        for (int i = 0; i < coverCount; i++)
        {
            Vector3 position = new Vector3(
                Random.Range(-25f, 25f),
                0f,
                Random.Range(-25f, 25f)
            );

            GameObject cover = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cover.name = $"Cover_{i + 1}";
            cover.transform.SetParent(coverContainer.transform);
            cover.transform.position = position;
            cover.transform.localScale = new Vector3(2f, 3f, 1f);

            // Random rotation
            cover.transform.rotation = Quaternion.Euler(0, Random.Range(0, 360), 0);

            // Cover material
            Material coverMat = new Material(Shader.Find("Standard"));
            coverMat.color = new Color(0.6f, 0.4f, 0.2f); // Brown
            cover.GetComponent<Renderer>().sharedMaterial = coverMat;
        }
    }

    static GameObject CreatePlayer(Transform parent)
    {
        GameObject player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        player.name = "Player";
        player.tag = "Player";
        player.transform.SetParent(parent);
        player.transform.localPosition = new Vector3(0, 1, 0);

        // Player components
        player.AddComponent<Rigidbody>();
        player.AddComponent<PlayerController>();
        player.AddComponent<HealthSystem>();

        // Player material
        Material playerMat = new Material(Shader.Find("Standard"));
        playerMat.color = Color.green;
        player.GetComponent<Renderer>().sharedMaterial = playerMat;

        return player;
    }

    static GameObject CreateSquadMateAgent(Transform parent)
    {
        GameObject agent = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        agent.name = "SquadMate_Agent";
        agent.transform.SetParent(parent);
        agent.transform.localPosition = new Vector3(2, 1, 0);

        // Agent components
        Rigidbody rb = agent.AddComponent<Rigidbody>();
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;

        SquadMateAgent agentScript = agent.AddComponent<SquadMateAgent>();
        BehaviorParameters behaviorParams = agent.AddComponent<BehaviorParameters>();

        // Configure behavior parameters for Unity 6
        behaviorParams.BehaviorName = "SquadMate";
        behaviorParams.BrainParameters.VectorObservationSize = 22; // Updated for PUBG observations
        behaviorParams.BrainParameters.NumStackedVectorObservations = 1;

        // Create ActionSpec for Unity 6 ML-Agents (correct way)
        var actionSpec = new Unity.MLAgents.Actuators.ActionSpec(
            numContinuousActions: 3,
            discreteBranchSizes: new int[] { 8 } // 8 possible discrete actions
        );

        // Set the ActionSpec
        behaviorParams.BrainParameters.ActionSpec = actionSpec;
        behaviorParams.TeamId = 0;
        behaviorParams.UseChildSensors = true;

        // Agent material
        Material agentMat = new Material(Shader.Find("Standard"));
        agentMat.color = Color.blue;
        agent.GetComponent<Renderer>().sharedMaterial = agentMat;

        return agent;
    }

    static void ConfigureEnvironment(GameEnvironment gameEnv, PUBGItemSystem itemSystem, GameObject player, GameObject squadmate)
    {
        // Configure GameEnvironment
        gameEnv.environmentSize = new Vector3(50f, 0f, 50f);
        gameEnv.maxEnemies = 5;
        gameEnv.maxMedkits = 3;
        gameEnv.maxWeapons = 2;
        gameEnv.player = player.transform;
        gameEnv.squadMate = squadmate.GetComponent<SquadMateAgent>();

        // Configure spawn points
        Transform spawnContainer = gameEnv.transform.Find("SpawnPoints");
        if (spawnContainer != null)
        {
            Transform[] spawnPoints = new Transform[spawnContainer.childCount];
            for (int i = 0; i < spawnContainer.childCount; i++)
            {
                spawnPoints[i] = spawnContainer.GetChild(i);
            }
            gameEnv.spawnPoints = spawnPoints;
        }

        // Configure cover points
        Transform coverContainer = gameEnv.transform.Find("CoverPoints");
        if (coverContainer != null)
        {
            Transform[] coverPoints = new Transform[coverContainer.childCount];
            for (int i = 0; i < coverContainer.childCount; i++)
            {
                coverPoints[i] = coverContainer.GetChild(i);
            }
            gameEnv.coverPoints = coverPoints;
        }

        // Configure PUBG Item System
        itemSystem.lootSpawnRadius = 40f;
        itemSystem.maxLootItems = 15;
        itemSystem.respawnInterval = 30f;

        Debug.Log("⚙️ Environment configuration complete");
    }

    static void CreateInitialEnemies(Transform parent, GameEnvironment gameEnv)
    {
        GameObject enemyContainer = new GameObject("Enemies");
        enemyContainer.transform.SetParent(parent);

        // Create 3 initial enemies
        for (int i = 0; i < 3; i++)
        {
            Vector3 position = new Vector3(
                Random.Range(-20f, 20f),
                1f,
                Random.Range(-20f, 20f)
            );

            GameObject enemy = CreateEnemy(position, enemyContainer.transform);

            // Note: GameEnvironment manages enemies internally through its spawning system
            // The enemies will be tracked automatically when the environment initializes
        }

        Debug.Log("👹 Created 3 initial enemies");
    }

    static GameObject CreateEnemy(Vector3 position, Transform parent)
    {
        GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        enemy.name = "Enemy";
        enemy.transform.SetParent(parent);
        enemy.transform.position = position;

        // Enemy components
        enemy.AddComponent<Rigidbody>();
        EnemyAI enemyAI = enemy.AddComponent<EnemyAI>();

        // Configure enemy stats
        enemyAI.health = Random.Range(80f, 120f);
        enemyAI.damage = Random.Range(20f, 35f);
        enemyAI.speed = Random.Range(2f, 4f);
        enemyAI.detectionRange = Random.Range(8f, 12f);
        enemyAI.attackRange = Random.Range(2f, 4f);
        enemyAI.aggressionLevel = Random.Range(0.5f, 1f);

        // Enemy material
        Material enemyMat = new Material(Shader.Find("Standard"));
        enemyMat.color = Color.red;
        enemy.GetComponent<Renderer>().sharedMaterial = enemyMat;

        return enemy;
    }

    static void SetupLootSpawning(PUBGItemSystem itemSystem)
    {
        // The item system will handle automatic loot spawning
        // Create initial item database if needed
        if (itemSystem.itemDatabase == null)
        {
            itemSystem.itemDatabase = ScriptableObject.CreateInstance<PUBGItemDatabase>();
            itemSystem.itemDatabase.InitializeDefaultItems();
        }

        Debug.Log("📦 Loot spawning system configured");
    }

    [MenuItem("SquadMate AI/🧪 Test PUBG Systems")]
    public static void TestPUBGSystems()
    {
        Debug.Log("🧪 Testing PUBG Systems...");

        // Find existing systems
        PUBGItemSystem itemSystem = FindObjectOfType<PUBGItemSystem>();
        SquadMateAgent agent = FindObjectOfType<SquadMateAgent>();

        if (itemSystem != null)
        {
            Debug.Log("✅ PUBG Item System found");
            itemSystem.SpawnRandomLoot();
        }
        else
        {
            Debug.LogWarning("❌ PUBG Item System not found");
        }

        if (agent != null)
        {
            Debug.Log("✅ SquadMate Agent found");
            if (agent.inventory != null)
            {
                agent.inventory.DebugPrintInventory();
            }
        }
        else
        {
            Debug.LogWarning("❌ SquadMate Agent not found");
        }
    }
}
