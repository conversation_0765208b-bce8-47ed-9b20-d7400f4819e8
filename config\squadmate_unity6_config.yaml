behaviors:
  SquadMate:
    trainer_type: ppo
    hyperparameters:
      batch_size: 2048 # Larger batch for Unity 6 performance
      buffer_size: 20480 # Increased buffer size
      learning_rate: 3.0e-4
      beta: 5.0e-3
      epsilon: 0.2
      lambd: 0.95
      num_epoch: 3
      learning_rate_schedule: linear

    network_settings:
      normalize: false
      hidden_units: 512 # Larger network for Unity 6
      num_layers: 3
      vis_encode_type: simple
      memory:
        sequence_length: 64 # Enhanced memory for better decisions
        memory_size: 256

    reward_signals:
      extrinsic:
        gamma: 0.99
        strength: 1.0
      curiosity:
        gamma: 0.99
        strength: 0.02
        encoding_size: 256
        learning_rate: 1e-3
      gail: # Unity 6 supports advanced imitation learning
        gamma: 0.99
        strength: 0.01
        demo_path: demonstrations/squadmate_demos.demo

    max_steps: 5000000 # Extended for PUBG tactical training
    time_horizon: 256 # Longer horizon for PUBG tactical decisions
    summary_freq: 5000 # More frequent summaries
    threaded: true # Enable threading for Unity 6

    self_play:
      save_steps: 25000
      team_change: 50000
      swap_steps: 5000
      play_against_latest_model_ratio: 0.5
      window: 20

    behavioral_cloning: # Unity 6 enhanced BC
      demo_path: demonstrations/squadmate_demos.demo
      strength: 0.5
      steps: 50000

env_settings:
  env_path: null
  env_args: null
  base_port: 5005
  num_envs: 1
  num_areas: 4 # Multiple training areas for Unity 6
  seed: -1
  max_lifetime_restarts: 10
  restarts_rate_limit_n: 1
  restarts_rate_limit_period_s: 60

engine_settings:
  width: 1920 # Unity 6 can handle higher resolution
  height: 1080
  quality_level: 0 # Use custom "Training" quality level
  time_scale: 15 # Unity 6 handles higher time scales better
  target_frame_rate: 60
  capture_frame_rate: 60
  no_graphics: false # Keep graphics for debugging in Unity 6

checkpoint_settings:
  run_id: pubg_squadmate_training
  initialize_from: null
  load_model: false
  resume: false
  force: false
  train_model: true
  inference: false
  results_dir: results
  keep_checkpoints: 10 # Keep more checkpoints in Unity 6

# Unity 6 specific optimizations
unity6_optimizations:
  use_burst_compilation: true
  enable_job_system: true
  batch_observations: true
  parallel_environments: true
  memory_optimization: true

# Advanced training features for Unity 6
advanced_features:
  curriculum_learning:
    enabled: true
    thresholds:
      basic_movement: 0.5
      formation_keeping: 1.0
      combat_engagement: 1.5
      tactical_behavior: 2.0

  multi_agent_training:
    enabled: true
    team_size: 2
    opponent_teams: 1

  dynamic_difficulty:
    enabled: true
    adjustment_frequency: 10000
    difficulty_range: [0.5, 2.0]
