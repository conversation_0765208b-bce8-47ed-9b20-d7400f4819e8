using UnityEngine;
using Unity.MLAgents;

/// <summary>
/// Enhanced training debugger with detailed real-time metrics
/// </summary>
public class EnhancedTrainingDebugger : MonoBehaviour
{
    [Header("Debug Settings")]
    public bool enableDebugger = true;
    public bool showDetailedMetrics = true;
    
    private Agent agent;
    private float lastReward = 0f;
    private int actionCount = 0;
    private float sessionStartTime;
    private Vector3 lastPosition;
    private float distanceTraveled = 0f;
    
    void Start()
    {
        agent = GetComponent<Agent>();
        sessionStartTime = Time.time;
        lastPosition = transform.position;
    }
    
    void Update()
    {
        if (!enableDebugger || agent == null) return;
        
        // Track distance traveled
        float frameDistance = Vector3.Distance(transform.position, lastPosition);
        distanceTraveled += frameDistance;
        lastPosition = transform.position;
        
        // Track actions
        actionCount++;
    }
    
    void OnGUI()
    {
        if (!enableDebugger || !showDetailedMetrics) return;
        
        // Debug panel (top-left)
        float panelWidth = 280f;
        float panelHeight = 200f;
        float margin = 15f;
        
        GUILayout.BeginArea(new Rect(margin, margin, panelWidth, panelHeight));
        GUILayout.BeginVertical("box");
        
        GUIStyle boldStyle = new GUIStyle(GUI.skin.label);
        boldStyle.fontStyle = FontStyle.Bold;
        GUILayout.Label("🧠 Enhanced Training Debug", boldStyle);
        
        if (agent != null)
        {
            GUILayout.Label($"🎯 Cumulative Reward: {agent.GetCumulativeReward():F3}");
            GUILayout.Label($"📊 Episode Count: {agent.CompletedEpisodes}");
            GUILayout.Label($"⏱️ Session Time: {(Time.time - sessionStartTime) / 60f:F1} min");
            GUILayout.Label($"🏃 Distance: {distanceTraveled:F1}m");
            GUILayout.Label($"⚡ Actions: {actionCount}");
            GUILayout.Label($"📍 Position: {transform.position:F1}");
            
            // Behavior info
            var behaviorParams = agent.GetComponent<Unity.MLAgents.Policies.BehaviorParameters>();
            if (behaviorParams != null)
            {
                GUILayout.Label($"🤖 Behavior: {behaviorParams.BehaviorName}");
                GUILayout.Label($"🎮 Mode: {behaviorParams.BehaviorType}");
                GUILayout.Label($"🧠 Model: {(behaviorParams.Model != null ? "Loaded" : "Training")}");
            }
        }
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
    
    // Called when agent receives reward
    public void OnRewardReceived(float reward)
    {
        lastReward = reward;
        if (enableDebugger && reward != 0)
        {
            Debug.Log($"🎯 Reward: {reward:F3} | Total: {agent?.GetCumulativeReward():F3}");
        }
    }
    
    // Called when episode begins
    public void OnEpisodeBegin()
    {
        actionCount = 0;
        distanceTraveled = 0f;
        lastPosition = transform.position;
        
        if (enableDebugger)
        {
            Debug.Log($"🔄 Episode {agent?.CompletedEpisodes} Started");
        }
    }
    
    // Called when episode ends
    public void OnEpisodeEnd()
    {
        if (enableDebugger && agent != null)
        {
            Debug.Log($"✅ Episode {agent.CompletedEpisodes} Complete:");
            Debug.Log($"   💰 Final Reward: {agent.GetCumulativeReward():F3}");
            Debug.Log($"   🏃 Distance: {distanceTraveled:F1}m");
            Debug.Log($"   ⚡ Actions: {actionCount}");
        }
    }
}
