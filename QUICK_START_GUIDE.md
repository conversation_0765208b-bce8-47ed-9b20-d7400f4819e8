# 🚀 PUBG SquadMate AI - Quick Start Guide

## 🎯 **Current Status: READY TO TRAIN!**

Your PUBG-style SquadMate AI system is fully implemented and ready for training! The training launcher is currently running and waiting for Unity setup.

## 🎮 **Step 1: Unity Setup (5 minutes)**

### **Open Unity 6000.1.6f1**
1. Launch Unity Hub
2. Open your SquadMate project (or create new 3D URP project)
3. Ensure Unity 6000.1.6f1 is being used

### **Install ML-Agents Package**
1. Go to `Window → Package Manager`
2. Click `+` → `Add package from git URL`
3. Enter: `com.unity.ml-agents`
4. Click `Add` (Unity 6 will auto-install compatible version)

### **Import PUBG System Files**
1. Copy all files from `Assets/` folder to your Unity project
2. Wait for Unity to compile scripts
3. Check console for any errors (should be none)

## 🏗️ **Step 2: Create Training Environment (2 minutes)**

### **Auto-Setup (Recommended)**
1. In Unity menu bar: `SquadMate AI → 🎯 Setup PUBG Training Environment`
2. This automatically creates:
   - Ground plane with cover objects
   - Enemy spawn points
   - Loot spawn system
   - Player and SquadMate agent
   - PUBG item database

### **Manual Verification**
Check that the scene contains:
- ✅ Ground plane
- ✅ Cover objects (brown cubes)
- ✅ Player (green capsule)
- ✅ SquadMate Agent (blue capsule)
- ✅ Spawn points (blue cylinders)

## ⚙️ **Step 3: Configure Training (1 minute)**

### **Agent Configuration**
1. Select the SquadMate Agent in hierarchy
2. Verify `BehaviorParameters` component has:
   - **Behavior Name**: `SquadMate`
   - **Vector Observation Size**: `22`
   - **Actions**: Continuous=3, Discrete=1 branch with 8 actions
   - **Behavior Type**: `Default`

### **Performance Settings**
1. Go to `Edit → Project Settings → Time`
2. Set **Time Scale** to `10-15` (for faster training)
3. Go to `Edit → Project Settings → Quality`
4. Create "Training" quality level with minimal settings

## 🚀 **Step 4: Start Training**

### **In Unity**
1. **Press PLAY** in Unity
2. Unity console should show: "Waiting for connection..."
3. Leave Unity running and playing

### **In Training Launcher**
1. Go back to the terminal running `start_pubg_training.py`
2. **Press ENTER** when Unity is playing
3. Training simulation will begin!

## 📊 **Step 5: Monitor Training (2-4 hours)**

### **What You'll See**
The AI will learn in phases:

**🎯 Phase 1 (0-500K steps): Basic Combat**
- Picking up weapons
- Basic enemy detection
- Simple attacks
- Following player

**🎯 Phase 2 (500K-1.5M steps): Tactical Awareness**
- Smart loot prioritization
- Effective healing
- Taking cover
- Inventory management

**🎯 Phase 3 (1.5M-3M steps): Advanced Tactics**
- Flanking enemies
- Team coordination
- Strategic healing
- Grenade usage

**🎯 Phase 4 (3M-5M steps): Pro-Level Play**
- Complex decisions
- Adaptive strategies
- Perfect optimization
- Seamless teamwork

### **Monitoring Tips**
- Watch Unity scene to see AI behavior
- Check console for training metrics
- Training saves automatically every 25K steps
- Can pause/resume anytime

## 🎯 **Expected Results**

### **After 2 Hours (Basic Training)**
- ✅ Picks up weapons and healing items
- ✅ Attacks enemies when armed
- ✅ Uses healing when low health
- ✅ Follows player at safe distance

### **After 4 Hours (Advanced Training)**
- ✅ Prioritizes high-value loot (M416 > pistol)
- ✅ Uses cover during combat
- ✅ Heals strategically (not during combat)
- ✅ Manages inventory weight
- ✅ Flanks enemies tactically
- ✅ Uses throwables effectively

## 🔧 **Troubleshooting**

### **Unity Issues**
- **Scripts won't compile**: Check all PUBG system files are imported
- **Agent not moving**: Verify BehaviorParameters configuration
- **No enemies**: Run the PUBG environment setup again

### **Training Issues**
- **Connection timeout**: Ensure Unity is playing and agent is in scene
- **Slow training**: Increase Time Scale to 15x
- **Memory issues**: Reduce batch size in config file

### **Performance Issues**
- **Low FPS**: Use "Training" quality level
- **High CPU**: Reduce number of enemies/loot items
- **Crashes**: Lower Time Scale to 10x

## 📁 **File Structure**

```
C:\Squadmate\
├── Assets\Scripts\
│   ├── Agents\SquadMateAgent.cs          # Enhanced AI agent
│   ├── Environment\PUBGItemSystem.cs     # Loot spawning
│   ├── Environment\PUBGItemDatabase.cs   # Item definitions
│   ├── Environment\PUBGInventory.cs      # Inventory system
│   └── Editor\PUBGTrainingSetup.cs       # Auto-setup tool
├── config\squadmate_unity6_config.yaml   # Training config
├── start_pubg_training.py                # Training launcher
└── PUBG_TRAINING_GUIDE.md               # Detailed guide
```

## 🎮 **Next Steps After Training**

1. **Export Model**: Training will offer to export .onnx file
2. **Deploy in Unity**: Copy model to project, set to Inference mode
3. **Test Performance**: Create test scenarios to validate AI
4. **Fine-tune**: Adjust rewards for specific behaviors
5. **Scale Up**: Add more enemies, larger environments

## 🏆 **Success Indicators**

Your AI is learning well when you see:
- ✅ Consistent weapon pickup behavior
- ✅ Smart healing timing (not during combat)
- ✅ Cover usage when under fire
- ✅ Loot prioritization (better items first)
- ✅ Team coordination with player
- ✅ Tactical positioning and movement

## 🎯 **Ready to Train!**

Your PUBG SquadMate AI system is now ready for advanced tactical training! Follow the steps above and watch your AI evolve from basic combat to pro-level PUBG tactics.

**Current Status**: Training launcher is waiting for Unity setup
**Next Action**: Complete Unity setup and press ENTER in terminal

🚀 **Happy Training!** 🎮
