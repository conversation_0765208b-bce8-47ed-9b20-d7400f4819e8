using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GameEnvironment : MonoBehaviour
{
    [Header("Environment Settings")]
    public Vector3 environmentSize = new Vector3(50f, 0f, 50f);
    public int maxEnemies = 5;
    public int maxMedkits = 3;
    public int maxWeapons = 2;

    [Header("🎒 PUBG Loot Settings")]
    public int maxBackpacks = 2;
    public int maxArmor = 3;
    public int maxAttachments = 5;
    public int maxThrowables = 4;
    public float lootRespawnTime = 30f;

    [Header("Prefabs")]
    public GameObject enemyPrefab;
    public GameObject medkitPrefab;
    public GameObject weaponPrefab;
    public GameObject playerPrefab;
    public GameObject squadMatePrefab;

    [Header("🎯 PUBG Loot Prefabs")]
    public GameObject[] weaponPrefabs; // AR, SMG, Sniper, Shotgun, Pistol
    public GameObject[] healingPrefabs; // Bandage, FirstAid, MedKit, EnergyDrink, Painkiller, Adrenaline
    public GameObject[] armorPrefabs; // Level 1-3 Helmet, Level 1-3 Vest
    public GameObject[] backpackPrefabs; // Level 1-3 Backpacks
    public GameObject[] attachmentPrefabs; // Scopes, Grips, Muzzles, Magazines
    public GameObject[] throwablePrefabs; // Frag, Smoke, Stun, Molotov

    [Header("Spawn Points")]
    public Transform[] spawnPoints;
    public Transform[] coverPoints;

    [Header("References")]
    public Transform player;
    public SquadMateAgent squadMate;

    private List<GameObject> enemies = new List<GameObject>();
    private List<GameObject> medkits = new List<GameObject>();
    private List<GameObject> weapons = new List<GameObject>();
    private ObjectSpawner spawner;

    void Start()
    {
        spawner = GetComponent<ObjectSpawner>();
        if (spawner == null)
        {
            spawner = gameObject.AddComponent<ObjectSpawner>();
        }

        InitializeEnvironment();
    }

    public void InitializeEnvironment()
    {
        // Clear existing objects
        ClearEnvironment();

        // Spawn player if not assigned
        if (player == null && playerPrefab != null)
        {
            Vector3 playerSpawnPos = GetRandomSpawnPoint();
            GameObject playerObj = Instantiate(playerPrefab, playerSpawnPos, Quaternion.identity);
            player = playerObj.transform;
        }

        // Spawn squadmate if not assigned
        if (squadMate == null && squadMatePrefab != null)
        {
            Vector3 squadMateSpawnPos = GetRandomSpawnPoint();
            GameObject squadMateObj = Instantiate(squadMatePrefab, squadMateSpawnPos, Quaternion.identity);
            squadMate = squadMateObj.GetComponent<SquadMateAgent>();
            squadMate.environment = this;
        }

        // Spawn enemies
        SpawnEnemies();

        // Spawn items
        SpawnMedkits();
        SpawnWeapons();
    }

    public void ResetEnvironment()
    {
        InitializeEnvironment();
    }

    private void ClearEnvironment()
    {
        // Clear enemies
        foreach (GameObject enemy in enemies)
        {
            if (enemy != null)
                DestroyImmediate(enemy);
        }
        enemies.Clear();

        // Clear medkits
        foreach (GameObject medkit in medkits)
        {
            if (medkit != null)
                DestroyImmediate(medkit);
        }
        medkits.Clear();

        // Clear weapons
        foreach (GameObject weapon in weapons)
        {
            if (weapon != null)
                DestroyImmediate(weapon);
        }
        weapons.Clear();
    }

    private void SpawnEnemies()
    {
        for (int i = 0; i < maxEnemies; i++)
        {
            if (enemyPrefab != null)
            {
                Vector3 spawnPos = GetRandomPosition();
                GameObject enemy = Instantiate(enemyPrefab, spawnPos, Quaternion.identity);
                enemies.Add(enemy);

                // Configure enemy
                EnemyController enemyController = enemy.GetComponent<EnemyController>();
                if (enemyController != null)
                {
                    enemyController.environment = this;
                }
            }
        }
    }

    private void SpawnMedkits()
    {
        for (int i = 0; i < maxMedkits; i++)
        {
            if (medkitPrefab != null)
            {
                Vector3 spawnPos = GetRandomPosition();
                GameObject medkit = Instantiate(medkitPrefab, spawnPos, Quaternion.identity);
                medkits.Add(medkit);
            }
        }
    }

    private void SpawnWeapons()
    {
        for (int i = 0; i < maxWeapons; i++)
        {
            if (weaponPrefab != null)
            {
                Vector3 spawnPos = GetRandomPosition();
                GameObject weapon = Instantiate(weaponPrefab, spawnPos, Quaternion.identity);
                weapons.Add(weapon);
            }
        }
    }

    public Vector3 GetRandomSpawnPoint()
    {
        if (spawnPoints != null && spawnPoints.Length > 0)
        {
            int randomIndex = Random.Range(0, spawnPoints.Length);
            return spawnPoints[randomIndex].position;
        }
        else
        {
            return GetRandomPosition();
        }
    }

    public Vector3 GetRandomPosition()
    {
        float x = Random.Range(-environmentSize.x / 2, environmentSize.x / 2);
        float z = Random.Range(-environmentSize.z / 2, environmentSize.z / 2);

        // Raycast to find ground level
        RaycastHit hit;
        Vector3 rayStart = new Vector3(x, 50f, z);

        if (Physics.Raycast(rayStart, Vector3.down, out hit, 100f))
        {
            return hit.point + Vector3.up * 0.1f;
        }
        else
        {
            return new Vector3(x, 0f, z);
        }
    }

    public Vector3 GetNearestCoverPoint(Vector3 fromPosition)
    {
        if (coverPoints == null || coverPoints.Length == 0)
            return fromPosition;

        Transform nearestCover = null;
        float minDistance = float.MaxValue;

        foreach (Transform cover in coverPoints)
        {
            if (cover == null) continue;

            float distance = Vector3.Distance(fromPosition, cover.position);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearestCover = cover;
            }
        }

        return nearestCover != null ? nearestCover.position : fromPosition;
    }

    public Transform[] GetEnemies()
    {
        List<Transform> activeEnemies = new List<Transform>();

        foreach (GameObject enemy in enemies)
        {
            if (enemy != null && enemy.activeInHierarchy)
            {
                activeEnemies.Add(enemy.transform);
            }
        }

        return activeEnemies.ToArray();
    }

    public Transform[] GetMedkits()
    {
        List<Transform> activeMedkits = new List<Transform>();

        foreach (GameObject medkit in medkits)
        {
            if (medkit != null && medkit.activeInHierarchy)
            {
                activeMedkits.Add(medkit.transform);
            }
        }

        return activeMedkits.ToArray();
    }

    public Transform[] GetWeapons()
    {
        List<Transform> activeWeapons = new List<Transform>();

        foreach (GameObject weapon in weapons)
        {
            if (weapon != null && weapon.activeInHierarchy)
            {
                activeWeapons.Add(weapon.transform);
            }
        }

        return activeWeapons.ToArray();
    }

    public void RemoveEnemy(GameObject enemy)
    {
        if (enemies.Contains(enemy))
        {
            enemies.Remove(enemy);
        }
    }

    public void RemoveMedkit(GameObject medkit)
    {
        if (medkits.Contains(medkit))
        {
            medkits.Remove(medkit);
        }
    }

    public void RemoveWeapon(GameObject weapon)
    {
        if (weapons.Contains(weapon))
        {
            weapons.Remove(weapon);
        }
    }

    // Respawn items periodically
    void Update()
    {
        // Respawn medkits if needed
        if (medkits.Count < maxMedkits && Random.Range(0f, 1f) < 0.001f)
        {
            SpawnMedkits();
        }

        // Respawn weapons if needed
        if (weapons.Count < maxWeapons && Random.Range(0f, 1f) < 0.0005f)
        {
            SpawnWeapons();
        }
    }

    void OnDrawGizmosSelected()
    {
        // Draw environment bounds
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireCube(transform.position, environmentSize);

        // Draw spawn points
        if (spawnPoints != null)
        {
            Gizmos.color = Color.green;
            foreach (Transform spawnPoint in spawnPoints)
            {
                if (spawnPoint != null)
                {
                    Gizmos.DrawWireSphere(spawnPoint.position, 1f);
                }
            }
        }

        // Draw cover points
        if (coverPoints != null)
        {
            Gizmos.color = Color.blue;
            foreach (Transform coverPoint in coverPoints)
            {
                if (coverPoint != null)
                {
                    Gizmos.DrawWireCube(coverPoint.position, Vector3.one * 2f);
                }
            }
        }
    }
}
