using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Curriculum Learning system that progressively increases training difficulty
/// </summary>
public class CurriculumLearning : MonoBehaviour
{
    [Header("Curriculum Settings")]
    public bool enableCurriculum = true;
    public float evaluationInterval = 300f; // 5 minutes
    public float successThreshold = 0.7f;
    public int evaluationEpisodes = 10;

    [Header("Current Curriculum Level")]
    public int currentLevel = 1;
    public string currentLevelName = "Basic Following";
    public float currentDifficulty = 0.1f;

    [Header("Performance Tracking")]
    public float averageReward = 0f;
    public float successRate = 0f;
    public bool readyForNextLevel = false;

    // Curriculum levels
    private List<CurriculumLevel> levels;
    private SquadMateAgent agent;
    private GameEnvironment environment;
    private float lastEvaluation;
    private List<float> recentRewards = new List<float>();
    private int episodeCount = 0;

    [System.Serializable]
    public class CurriculumLevel
    {
        public string name;
        public string description;
        public float difficulty;
        public int maxEnemies;
        public int maxMedkits;
        public int maxWeapons;
        public float playerMovementSpeed;
        public float environmentSize;
        public bool enableAdvancedFeatures;
        public string[] objectives;
    }

    void Start()
    {
        agent = FindObjectOfType<SquadMateAgent>();
        environment = FindObjectOfType<GameEnvironment>();

        InitializeCurriculumLevels();

        if (enableCurriculum)
        {
            Debug.Log("🎓 Curriculum Learning Started");
            ApplyCurrentLevel();
            InvokeRepeating(nameof(EvaluateProgress), evaluationInterval, evaluationInterval);
        }
    }

    void InitializeCurriculumLevels()
    {
        levels = new List<CurriculumLevel>
        {
            new CurriculumLevel
            {
                name = "Basic Following",
                description = "Learn to follow the player",
                difficulty = 0.1f,
                maxEnemies = 0,
                maxMedkits = 1,
                maxWeapons = 0,
                playerMovementSpeed = 3f,
                environmentSize = 20f,
                enableAdvancedFeatures = false,
                objectives = new string[] { "Stay close to player", "Basic movement" }
            },
            new CurriculumLevel
            {
                name = "Formation Keeping",
                description = "Maintain proper formation while moving",
                difficulty = 0.2f,
                maxEnemies = 1,
                maxMedkits = 2,
                maxWeapons = 1,
                playerMovementSpeed = 4f,
                environmentSize = 30f,
                enableAdvancedFeatures = false,
                objectives = new string[] { "Optimal distance", "Formation movement", "Basic combat awareness" }
            },
            new CurriculumLevel
            {
                name = "Combat Basics",
                description = "Learn basic combat and weapon usage",
                difficulty = 0.3f,
                maxEnemies = 2,
                maxMedkits = 2,
                maxWeapons = 2,
                playerMovementSpeed = 5f,
                environmentSize = 40f,
                enableAdvancedFeatures = false,
                objectives = new string[] { "Weapon pickup", "Enemy engagement", "Cover usage" }
            },
            new CurriculumLevel
            {
                name = "Health Management",
                description = "Learn health management and healing",
                difficulty = 0.4f,
                maxEnemies = 2,
                maxMedkits = 3,
                maxWeapons = 2,
                playerMovementSpeed = 5f,
                environmentSize = 40f,
                enableAdvancedFeatures = true,
                objectives = new string[] { "Health monitoring", "Medkit usage", "Damage avoidance" }
            },
            new CurriculumLevel
            {
                name = "Advanced Combat",
                description = "Complex combat scenarios",
                difficulty = 0.6f,
                maxEnemies = 3,
                maxMedkits = 2,
                maxWeapons = 3,
                playerMovementSpeed = 6f,
                environmentSize = 50f,
                enableAdvancedFeatures = true,
                objectives = new string[] { "Multi-enemy combat", "Tactical positioning", "Resource management" }
            },
            new CurriculumLevel
            {
                name = "Revive Mastery",
                description = "Master player revival under pressure",
                difficulty = 0.7f,
                maxEnemies = 3,
                maxMedkits = 2,
                maxWeapons = 2,
                playerMovementSpeed = 6f,
                environmentSize = 50f,
                enableAdvancedFeatures = true,
                objectives = new string[] { "Quick revives", "Combat revives", "Risk assessment" }
            },
            new CurriculumLevel
            {
                name = "Expert Squadmate",
                description = "Full complexity training",
                difficulty = 1.0f,
                maxEnemies = 5,
                maxMedkits = 3,
                maxWeapons = 3,
                playerMovementSpeed = 7f,
                environmentSize = 60f,
                enableAdvancedFeatures = true,
                objectives = new string[] { "All skills combined", "Adaptive behavior", "Expert decision making" }
            }
        };
    }

    void ApplyCurrentLevel()
    {
        if (currentLevel <= 0 || currentLevel > levels.Count) return;

        CurriculumLevel level = levels[currentLevel - 1];
        currentLevelName = level.name;
        currentDifficulty = level.difficulty;

        // Apply level settings to environment
        if (environment != null)
        {
            environment.maxEnemies = level.maxEnemies;
            environment.maxMedkits = level.maxMedkits;
            environment.maxWeapons = level.maxWeapons;
            environment.environmentSize = new Vector3(level.environmentSize, 0, level.environmentSize);
        }

        // Apply settings to player
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player != null)
        {
            player.moveSpeed = level.playerMovementSpeed;
        }

        Debug.Log($"🎓 Applied Curriculum Level {currentLevel}: {level.name}");
        Debug.Log($"   📊 Difficulty: {level.difficulty:P0}");
        Debug.Log($"   🎯 Objectives: {string.Join(", ", level.objectives)}");
        Debug.Log($"   ⚔️ Max Enemies: {level.maxEnemies}");
        Debug.Log($"   💊 Max Medkits: {level.maxMedkits}");
        Debug.Log($"   🔫 Max Weapons: {level.maxWeapons}");
    }

    void EvaluateProgress()
    {
        if (!enableCurriculum || agent == null) return;

        // Calculate performance metrics
        CalculatePerformanceMetrics();

        // Check if ready for next level
        CheckLevelProgression();

        Debug.Log($"🎓 Curriculum Evaluation - Level {currentLevel}:");
        Debug.Log($"   📊 Average Reward: {averageReward:F3}");
        Debug.Log($"   ✅ Success Rate: {successRate:P1}");
        Debug.Log($"   📈 Ready for Next: {readyForNextLevel}");
    }

    void CalculatePerformanceMetrics()
    {
        if (recentRewards.Count == 0) return;

        // Calculate average reward
        float sum = 0f;
        foreach (float reward in recentRewards)
        {
            sum += reward;
        }
        averageReward = sum / recentRewards.Count;

        // Calculate success rate (rewards above threshold)
        int successCount = 0;
        float threshold = currentDifficulty * 10f; // Dynamic threshold based on difficulty

        foreach (float reward in recentRewards)
        {
            if (reward > threshold)
                successCount++;
        }

        successRate = (float)successCount / recentRewards.Count;
    }

    void CheckLevelProgression()
    {
        // Check if performance meets criteria for next level
        bool performanceGood = averageReward > currentDifficulty * 5f;
        bool consistentSuccess = successRate >= successThreshold;
        bool hasNextLevel = currentLevel < levels.Count;

        readyForNextLevel = performanceGood && consistentSuccess && hasNextLevel;

        if (readyForNextLevel)
        {
            AdvanceToNextLevel();
        }
    }

    void AdvanceToNextLevel()
    {
        if (currentLevel >= levels.Count)
        {
            Debug.Log("🎉 CURRICULUM COMPLETED! Agent has mastered all levels!");
            return;
        }

        currentLevel++;
        recentRewards.Clear(); // Reset performance tracking
        readyForNextLevel = false;

        ApplyCurrentLevel();

        // Reinitialize environment with new settings
        if (environment != null)
        {
            environment.InitializeEnvironment();
        }

        Debug.Log($"🎓 LEVEL UP! Advanced to Level {currentLevel}");
        Debug.Log($"🎯 New Challenge: {currentLevelName}");

        // Give bonus reward for leveling up
        if (agent != null)
        {
            agent.AddReward(1.0f);
        }
    }

    // Called by agent when episode ends
    public void OnEpisodeEnd(float episodeReward)
    {
        recentRewards.Add(episodeReward);
        episodeCount++;

        // Keep only recent episodes for evaluation
        if (recentRewards.Count > evaluationEpisodes)
        {
            recentRewards.RemoveAt(0);
        }
    }

    // Manual level control
    [ContextMenu("Advance Level")]
    public void ForceAdvanceLevel()
    {
        if (currentLevel < levels.Count)
        {
            AdvanceToNextLevel();
        }
    }

    [ContextMenu("Reset to Level 1")]
    public void ResetToLevel1()
    {
        currentLevel = 1;
        recentRewards.Clear();
        ApplyCurrentLevel();
        Debug.Log("🔄 Reset to Level 1");
    }

    void OnGUI()
    {
        if (!enableCurriculum) return;

        // Curriculum info panel (top-center)
        float panelWidth = 300f;
        float panelHeight = 120f;
        float x = (Screen.width - panelWidth) / 2f;
        float y = 15f;

        GUILayout.BeginArea(new Rect(x, y, panelWidth, panelHeight));
        GUILayout.BeginVertical("box");

        GUIStyle boldStyle = new GUIStyle(GUI.skin.label);
        boldStyle.fontStyle = FontStyle.Bold;
        GUILayout.Label($"🎓 Curriculum Level {currentLevel}", boldStyle);
        GUILayout.Label($"📚 {currentLevelName}");
        GUILayout.Label($"📊 Difficulty: {currentDifficulty:P0}");
        GUILayout.Label($"💰 Avg Reward: {averageReward:F3}");
        GUILayout.Label($"✅ Success: {successRate:P1}");

        if (readyForNextLevel)
        {
            GUILayout.Label("🎯 Ready for Next Level!", boldStyle);
        }

        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}
