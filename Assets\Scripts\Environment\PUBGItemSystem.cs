using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// PUBG-style item system with comprehensive loot categories and AI prioritization
/// </summary>
public class PUBGItemSystem : MonoBehaviour
{
    [Header("🎯 PUBG Item Database")]
    public PUBGItemDatabase itemDatabase;
    
    [Header("🎒 Loot Spawn Settings")]
    public float lootSpawnRadius = 50f;
    public int maxLootItems = 20;
    public float respawnInterval = 45f;
    public AnimationCurve raritySpawnCurve = AnimationCurve.EaseInOut(0, 1, 1, 0.1f);
    
    [Header("🧠 AI Loot Priority")]
    public LootPriorityConfig aiPriorityConfig;
    
    private List<GameObject> activeLootItems = new List<GameObject>();
    private float lastSpawnTime;
    
    void Start()
    {
        InitializeItemDatabase();
        StartCoroutine(LootSpawnLoop());
    }
    
    void InitializeItemDatabase()
    {
        if (itemDatabase == null)
        {
            itemDatabase = ScriptableObject.CreateInstance<PUBGItemDatabase>();
            itemDatabase.InitializeDefaultItems();
        }
    }
    
    IEnumerator LootSpawnLoop()
    {
        while (true)
        {
            yield return new WaitForSeconds(respawnInterval);
            SpawnRandomLoot();
        }
    }
    
    public void SpawnRandomLoot()
    {
        if (activeLootItems.Count >= maxLootItems) return;
        
        // Choose random item category based on rarity
        PUBGItemType itemType = GetRandomItemType();
        PUBGItem itemData = itemDatabase.GetRandomItem(itemType);
        
        if (itemData != null)
        {
            Vector3 spawnPos = GetRandomSpawnPosition();
            GameObject lootItem = SpawnLootItem(itemData, spawnPos);
            activeLootItems.Add(lootItem);
        }
    }
    
    PUBGItemType GetRandomItemType()
    {
        float rand = Random.Range(0f, 1f);
        
        // Weighted spawn chances based on PUBG loot tables
        if (rand < 0.35f) return PUBGItemType.Weapon;
        else if (rand < 0.55f) return PUBGItemType.Healing;
        else if (rand < 0.70f) return PUBGItemType.Attachment;
        else if (rand < 0.85f) return PUBGItemType.Armor;
        else if (rand < 0.95f) return PUBGItemType.Throwable;
        else return PUBGItemType.Backpack;
    }
    
    Vector3 GetRandomSpawnPosition()
    {
        Vector2 randomCircle = Random.insideUnitCircle * lootSpawnRadius;
        Vector3 spawnPos = transform.position + new Vector3(randomCircle.x, 0, randomCircle.y);
        
        // Raycast to ground
        if (Physics.Raycast(spawnPos + Vector3.up * 10f, Vector3.down, out RaycastHit hit, 20f))
        {
            spawnPos = hit.point + Vector3.up * 0.5f;
        }
        
        return spawnPos;
    }
    
    GameObject SpawnLootItem(PUBGItem itemData, Vector3 position)
    {
        GameObject lootObject = new GameObject($"Loot_{itemData.itemName}");
        lootObject.transform.position = position;
        
        // Add visual representation
        MeshRenderer renderer = lootObject.AddComponent<MeshRenderer>();
        MeshFilter meshFilter = lootObject.AddComponent<MeshFilter>();
        
        // Create simple cube mesh for now (replace with actual models later)
        meshFilter.mesh = CreateItemMesh(itemData.itemType);
        renderer.material = CreateItemMaterial(itemData);
        
        // Add collider for pickup
        BoxCollider collider = lootObject.AddComponent<BoxCollider>();
        collider.isTrigger = true;
        collider.size = Vector3.one * 0.8f;
        
        // Add pickup component
        PUBGLootPickup pickup = lootObject.AddComponent<PUBGLootPickup>();
        pickup.itemData = itemData;
        pickup.itemSystem = this;
        
        // Add floating animation
        lootObject.AddComponent<LootFloatAnimation>();
        
        return lootObject;
    }
    
    Mesh CreateItemMesh(PUBGItemType itemType)
    {
        // Create different shapes for different item types
        switch (itemType)
        {
            case PUBGItemType.Weapon:
                return CreateCapsuleMesh();
            case PUBGItemType.Healing:
                return CreateSphereMesh();
            case PUBGItemType.Armor:
                return CreateCubeMesh();
            case PUBGItemType.Backpack:
                return CreateCubeMesh();
            case PUBGItemType.Attachment:
                return CreateCylinderMesh();
            case PUBGItemType.Throwable:
                return CreateSphereMesh();
            default:
                return CreateCubeMesh();
        }
    }
    
    Material CreateItemMaterial(PUBGItem itemData)
    {
        Material mat = new Material(Shader.Find("Standard"));
        
        // Color based on rarity
        switch (itemData.rarity)
        {
            case ItemRarity.Common:
                mat.color = Color.white;
                break;
            case ItemRarity.Uncommon:
                mat.color = Color.green;
                break;
            case ItemRarity.Rare:
                mat.color = Color.blue;
                break;
            case ItemRarity.Epic:
                mat.color = Color.magenta;
                break;
            case ItemRarity.Legendary:
                mat.color = Color.yellow;
                break;
        }
        
        mat.SetFloat("_Metallic", 0.3f);
        mat.SetFloat("_Smoothness", 0.6f);
        
        return mat;
    }
    
    // Simple mesh creation methods (replace with actual models)
    Mesh CreateCubeMesh()
    {
        GameObject cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
        Mesh mesh = cube.GetComponent<MeshFilter>().mesh;
        DestroyImmediate(cube);
        return mesh;
    }
    
    Mesh CreateSphereMesh()
    {
        GameObject sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        Mesh mesh = sphere.GetComponent<MeshFilter>().mesh;
        DestroyImmediate(sphere);
        return mesh;
    }
    
    Mesh CreateCapsuleMesh()
    {
        GameObject capsule = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        Mesh mesh = capsule.GetComponent<MeshFilter>().mesh;
        DestroyImmediate(capsule);
        return mesh;
    }
    
    Mesh CreateCylinderMesh()
    {
        GameObject cylinder = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        Mesh mesh = cylinder.GetComponent<MeshFilter>().mesh;
        DestroyImmediate(cylinder);
        return mesh;
    }
    
    public void RemoveLootItem(GameObject lootItem)
    {
        if (activeLootItems.Contains(lootItem))
        {
            activeLootItems.Remove(lootItem);
        }
    }
    
    public List<GameObject> GetNearbyLoot(Vector3 position, float radius)
    {
        List<GameObject> nearbyLoot = new List<GameObject>();
        
        foreach (GameObject loot in activeLootItems)
        {
            if (loot != null && Vector3.Distance(position, loot.transform.position) <= radius)
            {
                nearbyLoot.Add(loot);
            }
        }
        
        return nearbyLoot;
    }
    
    public GameObject GetBestLootForAgent(Vector3 agentPosition, SquadMateAgent agent)
    {
        List<GameObject> nearbyLoot = GetNearbyLoot(agentPosition, 15f);
        GameObject bestLoot = null;
        float bestPriority = 0f;
        
        foreach (GameObject loot in nearbyLoot)
        {
            PUBGLootPickup pickup = loot.GetComponent<PUBGLootPickup>();
            if (pickup != null)
            {
                float priority = CalculateLootPriority(pickup.itemData, agent);
                if (priority > bestPriority)
                {
                    bestPriority = priority;
                    bestLoot = loot;
                }
            }
        }
        
        return bestLoot;
    }
    
    float CalculateLootPriority(PUBGItem item, SquadMateAgent agent)
    {
        float priority = 0f;
        
        // Base priority from item rarity
        priority += (int)item.rarity * 0.2f;
        
        // Specific item type priorities based on agent needs
        switch (item.itemType)
        {
            case PUBGItemType.Weapon:
                if (!agent.hasWeapon) priority += 2.0f;
                else priority += 0.5f; // Upgrade potential
                break;
                
            case PUBGItemType.Healing:
                float healthPercent = agent.currentHealth / agent.maxHealth;
                priority += (1f - healthPercent) * 1.5f;
                break;
                
            case PUBGItemType.Armor:
                priority += 1.0f; // Always useful
                break;
                
            case PUBGItemType.Backpack:
                priority += 0.8f; // Inventory space
                break;
                
            case PUBGItemType.Attachment:
                if (agent.hasWeapon) priority += 0.7f;
                break;
                
            case PUBGItemType.Throwable:
                priority += 0.6f; // Tactical utility
                break;
        }
        
        return priority;
    }
}

/// <summary>
/// Floating animation for loot items
/// </summary>
public class LootFloatAnimation : MonoBehaviour
{
    public float floatHeight = 0.3f;
    public float floatSpeed = 2f;
    public float rotationSpeed = 30f;
    
    private Vector3 startPosition;
    
    void Start()
    {
        startPosition = transform.position;
    }
    
    void Update()
    {
        // Floating motion
        float newY = startPosition.y + Mathf.Sin(Time.time * floatSpeed) * floatHeight;
        transform.position = new Vector3(startPosition.x, newY, startPosition.z);
        
        // Rotation
        transform.Rotate(Vector3.up, rotationSpeed * Time.deltaTime);
    }
}
