using UnityEngine;
using UnityEditor;

/// <summary>
/// Automatically creates required tags for the combat training system
/// </summary>
public class TagSetup : EditorWindow
{
    [MenuItem("SquadMate AI/Setup/Create Required Tags")]
    public static void CreateRequiredTags()
    {
        Debug.Log("🏷️ Creating required tags for combat training...");
        
        string[] requiredTags = { "Enemy", "Weapon", "Medkit", "Player" };
        
        foreach (string tag in requiredTags)
        {
            CreateTag(tag);
        }
        
        Debug.Log("✅ All required tags created successfully!");
        EditorUtility.DisplayDialog("Tags Created!", 
            "Required tags have been created:\n\n" +
            "• Enemy\n" +
            "• Weapon\n" +
            "• Medkit\n" +
            "• Player\n\n" +
            "Combat training should now work properly!", "OK");
    }
    
    private static void CreateTag(string tagName)
    {
        // Get the tag manager
        SerializedObject tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
        SerializedProperty tagsProp = tagManager.FindProperty("tags");
        
        // Check if tag already exists
        bool found = false;
        for (int i = 0; i < tagsProp.arraySize; i++)
        {
            SerializedProperty t = tagsProp.GetArrayElementAtIndex(i);
            if (t.stringValue.Equals(tagName))
            {
                found = true;
                break;
            }
        }
        
        // Add tag if it doesn't exist
        if (!found)
        {
            tagsProp.InsertArrayElementAtIndex(0);
            SerializedProperty newTagProp = tagsProp.GetArrayElementAtIndex(0);
            newTagProp.stringValue = tagName;
            tagManager.ApplyModifiedProperties();
            Debug.Log($"✅ Created tag: {tagName}");
        }
        else
        {
            Debug.Log($"ℹ️ Tag already exists: {tagName}");
        }
    }
    
    [MenuItem("SquadMate AI/Setup/Validate Tags")]
    public static void ValidateTags()
    {
        string[] requiredTags = { "Enemy", "Weapon", "Medkit", "Player" };
        bool allTagsExist = true;
        string missingTags = "";
        
        foreach (string tag in requiredTags)
        {
            if (!TagExists(tag))
            {
                allTagsExist = false;
                missingTags += "• " + tag + "\n";
            }
        }
        
        if (allTagsExist)
        {
            Debug.Log("✅ All required tags exist!");
            EditorUtility.DisplayDialog("Tags Valid", "All required tags exist and are ready for use!", "OK");
        }
        else
        {
            Debug.LogWarning($"⚠️ Missing tags:\n{missingTags}");
            EditorUtility.DisplayDialog("Missing Tags", 
                $"The following tags are missing:\n\n{missingTags}\nUse 'Create Required Tags' to fix this.", "OK");
        }
    }
    
    private static bool TagExists(string tagName)
    {
        SerializedObject tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
        SerializedProperty tagsProp = tagManager.FindProperty("tags");
        
        for (int i = 0; i < tagsProp.arraySize; i++)
        {
            SerializedProperty t = tagsProp.GetArrayElementAtIndex(i);
            if (t.stringValue.Equals(tagName))
            {
                return true;
            }
        }
        return false;
    }
}
