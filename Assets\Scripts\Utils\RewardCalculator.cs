using UnityEngine;

public class RewardCalculator : MonoBehaviour
{
    [Header("Reward Settings - Optimized for Faster Learning")]
    public float survivalReward = 0.002f;          // Increased survival reward
    public float formationReward = 0.02f;          // Doubled formation reward
    public float reviveReward = 1.0f;              // Doubled revive reward
    public float eliminationReward = 0.6f;         // Doubled elimination reward
    public float healingReward = 0.4f;             // Doubled healing reward
    public float weaponPickupReward = 0.2f;        // Doubled weapon pickup reward
    public float damagePenalty = -0.2f;            // Increased damage penalty
    public float deathPenalty = -2.0f;             // Doubled death penalty
    public float playerDeathPenalty = -1.0f;       // Doubled player death penalty
    public float distancePenalty = -0.01f;         // Doubled distance penalty

    [Header("Formation Settings")]
    public float optimalDistance = 3f;
    public float maxFormationDistance = 10f;

    private SquadMateAgent agent;
    private Vector3 lastAgentPosition;
    private Vector3 lastPlayerPosition;
    private float lastHealth;
    private int lastEnemyCount;

    void Start()
    {
        agent = GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            lastAgentPosition = agent.transform.position;
            lastHealth = agent.currentHealth;
        }
    }

    public void CalculateFrameRewards(SquadMateAgent squadMate)
    {
        if (squadMate == null) return;

        // Basic survival reward
        squadMate.AddReward(survivalReward);

        // Formation keeping rewards
        CalculateFormationRewards(squadMate);

        // Combat rewards
        CalculateCombatRewards(squadMate);

        // Health management rewards
        CalculateHealthRewards(squadMate);

        // Movement efficiency rewards
        CalculateMovementRewards(squadMate);

        // Update tracking variables
        UpdateTrackingVariables(squadMate);
    }

    private void CalculateFormationRewards(SquadMateAgent squadMate)
    {
        if (squadMate.player == null) return;

        float distanceToPlayer = Vector3.Distance(squadMate.transform.position, squadMate.player.position);

        // Optimal formation distance reward
        if (distanceToPlayer >= optimalDistance * 0.7f && distanceToPlayer <= optimalDistance * 1.3f)
        {
            squadMate.AddReward(formationReward);
        }
        // Penalty for being too far
        else if (distanceToPlayer > maxFormationDistance)
        {
            float penalty = distancePenalty * (distanceToPlayer / maxFormationDistance);
            squadMate.AddReward(penalty);
        }
        // Small penalty for being too close (might block player)
        else if (distanceToPlayer < optimalDistance * 0.3f)
        {
            squadMate.AddReward(distancePenalty * 0.5f);
        }

        // Reward for facing threats while staying in formation
        Transform nearestEnemy = GetNearestEnemy(squadMate);
        if (nearestEnemy != null)
        {
            Vector3 enemyDirection = (nearestEnemy.position - squadMate.transform.position).normalized;
            Vector3 agentForward = squadMate.transform.forward;
            float dot = Vector3.Dot(agentForward, enemyDirection);

            // Reward for facing enemies
            if (dot > 0.7f)
            {
                squadMate.AddReward(0.005f);
            }
        }
    }

    private void CalculateCombatRewards(SquadMateAgent squadMate)
    {
        // Check if enemy count decreased (enemy eliminated)
        Transform[] currentEnemies = squadMate.environment.GetEnemies();
        int currentEnemyCount = currentEnemies.Length;

        if (currentEnemyCount < lastEnemyCount)
        {
            squadMate.AddReward(eliminationReward);
        }

        lastEnemyCount = currentEnemyCount;

        // Reward for having weapon when enemies are nearby
        Transform nearestEnemy = GetNearestEnemy(squadMate);
        if (nearestEnemy != null && squadMate.hasWeapon)
        {
            float enemyDistance = Vector3.Distance(squadMate.transform.position, nearestEnemy.position);
            if (enemyDistance < 15f)
            {
                squadMate.AddReward(0.002f); // Small reward for being armed near threats
            }
        }

        // Penalty for not having weapon when enemies are very close
        if (nearestEnemy != null && !squadMate.hasWeapon)
        {
            float enemyDistance = Vector3.Distance(squadMate.transform.position, nearestEnemy.position);
            if (enemyDistance < 8f)
            {
                squadMate.AddReward(-0.01f);
            }
        }
    }

    private void CalculateHealthRewards(SquadMateAgent squadMate)
    {
        // Penalty for taking damage
        if (squadMate.currentHealth < lastHealth)
        {
            float damageTaken = lastHealth - squadMate.currentHealth;
            squadMate.AddReward(damagePenalty * (damageTaken / 100f));
        }

        // Reward for healing when health is low
        if (squadMate.currentHealth > lastHealth && lastHealth < squadMate.maxHealth * 0.5f)
        {
            squadMate.AddReward(healingReward * 0.5f);
        }

        // Penalty for staying at low health when medkits are available
        if (squadMate.currentHealth < squadMate.maxHealth * 0.3f)
        {
            Transform[] medkits = squadMate.environment.GetMedkits();
            if (medkits.Length > 0)
            {
                squadMate.AddReward(-0.001f);
            }
        }
    }

    private void CalculateMovementRewards(SquadMateAgent squadMate)
    {
        // Reward for moving towards objectives
        Vector3 currentPosition = squadMate.transform.position;

        // If low health, reward moving towards medkits
        if (squadMate.currentHealth < squadMate.maxHealth * 0.5f)
        {
            Transform nearestMedkit = GetNearestMedkit(squadMate);
            if (nearestMedkit != null)
            {
                float lastDistanceToMedkit = Vector3.Distance(lastAgentPosition, nearestMedkit.position);
                float currentDistanceToMedkit = Vector3.Distance(currentPosition, nearestMedkit.position);

                if (currentDistanceToMedkit < lastDistanceToMedkit)
                {
                    squadMate.AddReward(0.005f); // Reward for moving towards medkit
                }
            }
        }

        // If no weapon, reward moving towards weapons
        if (!squadMate.hasWeapon)
        {
            Transform nearestWeapon = GetNearestWeapon(squadMate);
            if (nearestWeapon != null)
            {
                float lastDistanceToWeapon = Vector3.Distance(lastAgentPosition, nearestWeapon.position);
                float currentDistanceToWeapon = Vector3.Distance(currentPosition, nearestWeapon.position);

                if (currentDistanceToWeapon < lastDistanceToWeapon)
                {
                    squadMate.AddReward(0.003f); // Reward for moving towards weapon
                }
            }
        }

        // Penalty for staying in the same place too long (encourage exploration)
        float movementDistance = Vector3.Distance(currentPosition, lastAgentPosition);
        if (movementDistance < 0.1f)
        {
            squadMate.AddReward(-0.0005f);
        }
    }

    private void UpdateTrackingVariables(SquadMateAgent squadMate)
    {
        lastAgentPosition = squadMate.transform.position;
        lastHealth = squadMate.currentHealth;

        if (squadMate.player != null)
        {
            lastPlayerPosition = squadMate.player.position;
        }
    }

    private Transform GetNearestEnemy(SquadMateAgent squadMate)
    {
        Transform[] enemies = squadMate.environment.GetEnemies();
        if (enemies.Length == 0) return null;

        Transform nearest = null;
        float minDistance = float.MaxValue;

        foreach (Transform enemy in enemies)
        {
            if (enemy == null) continue;

            float distance = Vector3.Distance(squadMate.transform.position, enemy.position);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearest = enemy;
            }
        }

        return nearest;
    }

    private Transform GetNearestMedkit(SquadMateAgent squadMate)
    {
        Transform[] medkits = squadMate.environment.GetMedkits();
        if (medkits.Length == 0) return null;

        Transform nearest = null;
        float minDistance = float.MaxValue;

        foreach (Transform medkit in medkits)
        {
            if (medkit == null) continue;

            float distance = Vector3.Distance(squadMate.transform.position, medkit.position);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearest = medkit;
            }
        }

        return nearest;
    }

    private Transform GetNearestWeapon(SquadMateAgent squadMate)
    {
        Transform[] weapons = squadMate.environment.GetWeapons();
        if (weapons.Length == 0) return null;

        Transform nearest = null;
        float minDistance = float.MaxValue;

        foreach (Transform weapon in weapons)
        {
            if (weapon == null) continue;

            float distance = Vector3.Distance(squadMate.transform.position, weapon.position);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearest = weapon;
            }
        }

        return nearest;
    }

    // Public methods for external reward triggers
    public void OnReviveComplete(SquadMateAgent squadMate)
    {
        squadMate.AddReward(reviveReward);
    }

    public void OnWeaponPickup(SquadMateAgent squadMate)
    {
        squadMate.AddReward(weaponPickupReward);
    }

    public void OnEnemyEliminated(SquadMateAgent squadMate)
    {
        squadMate.AddReward(eliminationReward);
    }

    public void OnPlayerDeath(SquadMateAgent squadMate)
    {
        squadMate.AddReward(playerDeathPenalty);
    }

    public void OnAgentDeath(SquadMateAgent squadMate)
    {
        squadMate.AddReward(deathPenalty);
    }
}
