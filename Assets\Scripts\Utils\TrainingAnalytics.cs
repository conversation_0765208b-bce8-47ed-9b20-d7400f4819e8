using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Linq;

/// <summary>
/// Advanced training analytics and data collection
/// </summary>
public class TrainingAnalytics : MonoBehaviour
{
    [Header("Analytics Settings")]
    public bool enableAnalytics = true;
    public bool saveToFile = true;
    public float dataCollectionInterval = 5f;
    public int maxDataPoints = 1000;

    [Header("File Settings")]
    public string analyticsFileName = "training_analytics.csv";

    [Header("Current Session Stats")]
    public float sessionStartTime;
    public int totalEpisodes;
    public float totalRewards;
    public float averageEpisodeLength;
    public float learningEfficiency;

    // Data structures
    private List<TrainingDataPoint> trainingData = new List<TrainingDataPoint>();
    private SquadMateAgent agent;
    private PlayerController player;
    private GameEnvironment environment;
    private float lastDataCollection;
    private int lastEpisodeCount;
    private float lastTotalRewards;

    [System.Serializable]
    public class TrainingDataPoint
    {
        public float timestamp;
        public int episode;
        public float reward;
        public float agentHealth;
        public float playerHealth;
        public Vector3 agentPosition;
        public Vector3 playerPosition;
        public int enemyCount;
        public int medkitCount;
        public int weaponCount;
        public string agentState;
        public float distanceToPlayer;
        public bool hasWeapon;
        public float fps;
        public float timeScale;
    }

    void Start()
    {
        sessionStartTime = Time.time;

        // Find components
        agent = FindObjectOfType<SquadMateAgent>();
        player = FindObjectOfType<PlayerController>();
        environment = FindObjectOfType<GameEnvironment>();

        if (enableAnalytics)
        {
            Debug.Log("📊 Training Analytics Started");
            InvokeRepeating(nameof(CollectData), dataCollectionInterval, dataCollectionInterval);
        }

        // Create CSV header if saving to file
        if (saveToFile)
        {
            CreateCSVHeader();
        }
    }

    void CollectData()
    {
        if (!enableAnalytics || agent == null) return;

        TrainingDataPoint dataPoint = new TrainingDataPoint
        {
            timestamp = Time.time - sessionStartTime,
            episode = totalEpisodes,
            reward = agent.GetCumulativeReward(),
            agentHealth = agent.currentHealth,
            playerHealth = player != null ? player.currentHealth : 0f,
            agentPosition = agent.transform.position,
            playerPosition = player != null ? player.transform.position : Vector3.zero,
            enemyCount = environment != null ? environment.GetEnemies().Length : 0,
            medkitCount = environment != null ? environment.GetMedkits().Length : 0,
            weaponCount = environment != null ? environment.GetWeapons().Length : 0,
            agentState = agent.currentState.ToString(),
            distanceToPlayer = player != null ? Vector3.Distance(agent.transform.position, player.transform.position) : 0f,
            hasWeapon = agent.hasWeapon,
            fps = 1f / Time.unscaledDeltaTime,
            timeScale = Time.timeScale
        };

        trainingData.Add(dataPoint);

        // Limit data points to prevent memory issues
        if (trainingData.Count > maxDataPoints)
        {
            trainingData.RemoveAt(0);
        }

        // Save to file if enabled
        if (saveToFile)
        {
            SaveDataPointToCSV(dataPoint);
        }

        // Update session stats
        UpdateSessionStats();
    }

    void UpdateSessionStats()
    {
        if (trainingData.Count == 0) return;

        // Calculate averages and trends
        totalRewards = trainingData.Sum(d => d.reward);

        if (trainingData.Count > 1)
        {
            var recentData = trainingData.TakeLast(10).ToList();
            averageEpisodeLength = recentData.Average(d => d.timestamp);

            // Calculate learning efficiency (reward improvement over time)
            var firstHalf = trainingData.Take(trainingData.Count / 2).Average(d => d.reward);
            var secondHalf = trainingData.Skip(trainingData.Count / 2).Average(d => d.reward);
            learningEfficiency = secondHalf - firstHalf;
        }
    }

    void CreateCSVHeader()
    {
        string filePath = Path.Combine(Application.persistentDataPath, analyticsFileName);

        if (!File.Exists(filePath))
        {
            string header = "Timestamp,Episode,Reward,AgentHealth,PlayerHealth,AgentPosX,AgentPosY,AgentPosZ," +
                          "PlayerPosX,PlayerPosY,PlayerPosZ,EnemyCount,MedkitCount,WeaponCount," +
                          "AgentState,DistanceToPlayer,HasWeapon,FPS,TimeScale\n";

            File.WriteAllText(filePath, header);
            Debug.Log($"📁 Analytics CSV created: {filePath}");
        }
    }

    void SaveDataPointToCSV(TrainingDataPoint data)
    {
        string filePath = Path.Combine(Application.persistentDataPath, analyticsFileName);

        string line = $"{data.timestamp:F2},{data.episode},{data.reward:F3},{data.agentHealth:F1}," +
                     $"{data.playerHealth:F1},{data.agentPosition.x:F2},{data.agentPosition.y:F2}," +
                     $"{data.agentPosition.z:F2},{data.playerPosition.x:F2},{data.playerPosition.y:F2}," +
                     $"{data.playerPosition.z:F2},{data.enemyCount},{data.medkitCount},{data.weaponCount}," +
                     $"{data.agentState},{data.distanceToPlayer:F2},{data.hasWeapon},{data.fps:F0},{data.timeScale:F1}\n";

        File.AppendAllText(filePath, line);
    }

    // Public methods for external access
    public List<TrainingDataPoint> GetTrainingData()
    {
        return new List<TrainingDataPoint>(trainingData);
    }

    public float GetAverageReward()
    {
        return trainingData.Count > 0 ? trainingData.Average(d => d.reward) : 0f;
    }

    public float GetRewardTrend()
    {
        if (trainingData.Count < 10) return 0f;

        var recent = trainingData.TakeLast(5).Average(d => d.reward);
        var older = trainingData.Skip(trainingData.Count - 10).Take(5).Average(d => d.reward);

        return recent - older;
    }

    public void ExportAnalytics()
    {
        string filePath = Path.Combine(Application.persistentDataPath, $"analytics_export_{System.DateTime.Now:yyyyMMdd_HHmmss}.json");

        var exportData = new
        {
            sessionStartTime = sessionStartTime,
            sessionDuration = Time.time - sessionStartTime,
            totalEpisodes = totalEpisodes,
            totalRewards = totalRewards,
            averageReward = GetAverageReward(),
            rewardTrend = GetRewardTrend(),
            learningEfficiency = learningEfficiency,
            dataPoints = trainingData
        };

        string json = JsonUtility.ToJson(exportData, true);
        File.WriteAllText(filePath, json);

        Debug.Log($"📊 Analytics exported to: {filePath}");
    }

    public void ResetSession()
    {
        trainingData.Clear();
        totalEpisodes = 0;
        totalRewards = 0f;
        sessionStartTime = Time.time;
        learningEfficiency = 0f;
        Debug.Log("🔄 Training analytics session reset");
    }

    void OnGUI()
    {
        if (!enableAnalytics) return;

        // Analytics panel (bottom-left)
        float panelWidth = 250f;
        float panelHeight = 150f;
        float margin = 15f;

        GUILayout.BeginArea(new Rect(margin, Screen.height - panelHeight - margin, panelWidth, panelHeight));
        GUILayout.BeginVertical("box");

        GUIStyle boldStyle = new GUIStyle(GUI.skin.label);
        boldStyle.fontStyle = FontStyle.Bold;
        GUILayout.Label("📊 Training Analytics", boldStyle);
        GUILayout.Label($"Session: {(Time.time - sessionStartTime) / 60f:F1} min");
        GUILayout.Label($"Episodes: {totalEpisodes}");
        GUILayout.Label($"Avg Reward: {GetAverageReward():F3}");
        GUILayout.Label($"Reward Trend: {GetRewardTrend():F3}");
        GUILayout.Label($"Learning Eff: {learningEfficiency:F3}");
        GUILayout.Label($"Data Points: {trainingData.Count}");

        if (GUILayout.Button("📁 Export Analytics"))
        {
            ExportAnalytics();
        }

        GUILayout.EndVertical();
        GUILayout.EndArea();
    }

    void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus && saveToFile)
        {
            ExportAnalytics();
        }
    }

    void OnApplicationQuit()
    {
        if (saveToFile)
        {
            ExportAnalytics();
        }
    }
}
