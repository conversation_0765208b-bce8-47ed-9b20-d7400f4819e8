using UnityEngine;
using System.Collections.Generic;
using System.Collections;

/// <summary>
/// Advanced combat training environment with enemies, weapons, and medkits
/// Teaches AI aggressive and defensive tactics like a pro player
/// </summary>
public class CombatTrainingEnvironment : MonoBehaviour
{
    [Header("🎯 Combat Training Settings")]
    public bool enableCombatTraining = true;
    public float spawnRadius = 15f;
    public float safeZoneRadius = 3f;

    [Header("⚔️ Enemy Settings")]
    public int maxEnemies = 5;
    public float enemySpawnInterval = 10f;
    public float enemyHealth = 100f;
    public float enemyDamage = 25f;
    public float enemySpeed = 3f;
    public float enemyDetectionRange = 8f;
    public float enemyAttackRange = 2f;

    [Header("🔫 Weapon Settings")]
    public int maxWeapons = 3;
    public float weaponSpawnInterval = 15f;
    public string[] weaponTypes = { "Rifle", "Shotgun", "Pistol", "SMG" };
    public float[] weaponDamage = { 35f, 60f, 20f, 25f };
    public float[] weaponRange = { 15f, 5f, 10f, 8f };
    public float[] weaponFireRate = { 0.3f, 1.0f, 0.5f, 0.1f };

    [Header("💊 Medkit Settings")]
    public int maxMedkits = 4;
    public float medkitSpawnInterval = 20f;
    public float medkitHealAmount = 50f;

    [Header("🎮 Training Scenarios")]
    public TrainingScenario currentScenario = TrainingScenario.Balanced;
    public float scenarioSwitchInterval = 120f; // 2 minutes

    [Header("🧠 AI Behavior Training")]
    public bool trainAggressiveBehavior = true;
    public bool trainDefensiveBehavior = true;
    public bool trainTacticalPositioning = true;
    public bool trainResourceManagement = true;

    // Internal tracking
    private List<GameObject> activeEnemies = new List<GameObject>();
    private List<GameObject> activeWeapons = new List<GameObject>();
    private List<GameObject> activeMedkits = new List<GameObject>();
    private SquadMateAgent agent;
    private PlayerController player;
    private float lastEnemySpawn;
    private float lastWeaponSpawn;
    private float lastMedkitSpawn;
    private float lastScenarioSwitch;

    public enum TrainingScenario
    {
        Aggressive,     // Many enemies, few medkits - teach aggressive play
        Defensive,      // Few enemies, many medkits - teach defensive play
        Balanced,       // Equal mix - teach adaptability
        Survival,       // High enemy count, limited resources
        ResourceRich,   // Many weapons and medkits, moderate enemies
        Tactical        // Strategic positioning focus
    }

    void Start()
    {
        agent = FindObjectOfType<SquadMateAgent>();
        player = FindObjectOfType<PlayerController>();

        if (enableCombatTraining)
        {
            Debug.Log("🎯 Combat Training Environment Started");
            Debug.Log($"📊 Scenario: {currentScenario}");
            StartCoroutine(ManageTrainingEnvironment());
        }
    }

    IEnumerator ManageTrainingEnvironment()
    {
        while (enableCombatTraining)
        {
            // Switch training scenarios periodically
            if (Time.time - lastScenarioSwitch > scenarioSwitchInterval)
            {
                SwitchTrainingScenario();
                lastScenarioSwitch = Time.time;
            }

            // Spawn entities based on current scenario
            SpawnEnemiesIfNeeded();
            SpawnWeaponsIfNeeded();
            SpawnMedkitsIfNeeded();

            // Clean up destroyed objects
            CleanupDestroyedObjects();

            yield return new WaitForSeconds(1f);
        }
    }

    void SwitchTrainingScenario()
    {
        // Cycle through scenarios for comprehensive training
        int nextScenario = ((int)currentScenario + 1) % System.Enum.GetValues(typeof(TrainingScenario)).Length;
        currentScenario = (TrainingScenario)nextScenario;

        Debug.Log($"🔄 Switching to {currentScenario} Training Scenario");

        // Adjust spawn parameters based on scenario
        ApplyScenarioSettings();

        // Clear existing spawns to reset environment
        ClearAllSpawns();
    }

    void ApplyScenarioSettings()
    {
        switch (currentScenario)
        {
            case TrainingScenario.Aggressive:
                maxEnemies = 6;
                maxWeapons = 4;
                maxMedkits = 1;
                enemySpawnInterval = 8f;
                Debug.Log("🔥 AGGRESSIVE MODE: High enemy count, limited healing");
                break;

            case TrainingScenario.Defensive:
                maxEnemies = 2;
                maxWeapons = 2;
                maxMedkits = 5;
                enemySpawnInterval = 15f;
                Debug.Log("🛡️ DEFENSIVE MODE: Few enemies, abundant healing");
                break;

            case TrainingScenario.Balanced:
                maxEnemies = 3;
                maxWeapons = 3;
                maxMedkits = 3;
                enemySpawnInterval = 12f;
                Debug.Log("⚖️ BALANCED MODE: Equal resources and threats");
                break;

            case TrainingScenario.Survival:
                maxEnemies = 8;
                maxWeapons = 2;
                maxMedkits = 2;
                enemySpawnInterval = 6f;
                Debug.Log("💀 SURVIVAL MODE: Overwhelming enemies, scarce resources");
                break;

            case TrainingScenario.ResourceRich:
                maxEnemies = 3;
                maxWeapons = 5;
                maxMedkits = 4;
                enemySpawnInterval = 10f;
                Debug.Log("💎 RESOURCE RICH MODE: Abundant equipment");
                break;

            case TrainingScenario.Tactical:
                maxEnemies = 4;
                maxWeapons = 3;
                maxMedkits = 2;
                enemySpawnInterval = 10f;
                enemyDetectionRange = 12f; // Longer range for tactical play
                Debug.Log("🎯 TACTICAL MODE: Strategic positioning focus");
                break;
        }
    }

    void SpawnEnemiesIfNeeded()
    {
        if (activeEnemies.Count < maxEnemies && Time.time - lastEnemySpawn > enemySpawnInterval)
        {
            SpawnEnemy();
            lastEnemySpawn = Time.time;
        }
    }

    void SpawnWeaponsIfNeeded()
    {
        if (activeWeapons.Count < maxWeapons && Time.time - lastWeaponSpawn > weaponSpawnInterval)
        {
            SpawnWeapon();
            lastWeaponSpawn = Time.time;
        }
    }

    void SpawnMedkitsIfNeeded()
    {
        if (activeMedkits.Count < maxMedkits && Time.time - lastMedkitSpawn > medkitSpawnInterval)
        {
            SpawnMedkit();
            lastMedkitSpawn = Time.time;
        }
    }

    void SpawnEnemy()
    {
        Vector3 spawnPos = GetRandomSpawnPosition();
        GameObject enemy = CreateEnemy(spawnPos);
        activeEnemies.Add(enemy);

        Debug.Log($"👹 Enemy spawned at {spawnPos} | Active: {activeEnemies.Count}/{maxEnemies}");
    }

    void SpawnWeapon()
    {
        Vector3 spawnPos = GetRandomSpawnPosition();
        GameObject weapon = CreateWeapon(spawnPos);
        activeWeapons.Add(weapon);

        Debug.Log($"🔫 Weapon spawned at {spawnPos} | Active: {activeWeapons.Count}/{maxWeapons}");
    }

    void SpawnMedkit()
    {
        Vector3 spawnPos = GetRandomSpawnPosition();
        GameObject medkit = CreateMedkit(spawnPos);
        activeMedkits.Add(medkit);

        Debug.Log($"💊 Medkit spawned at {spawnPos} | Active: {activeMedkits.Count}/{maxMedkits}");
    }

    Vector3 GetRandomSpawnPosition()
    {
        Vector3 playerPos = player != null ? player.transform.position : Vector3.zero;

        // Spawn outside safe zone but within spawn radius
        Vector3 randomDir = Random.insideUnitCircle.normalized;
        float distance = Random.Range(safeZoneRadius + 2f, spawnRadius);

        Vector3 spawnPos = playerPos + new Vector3(randomDir.x, 0, randomDir.y) * distance;
        spawnPos.y = 0.5f; // Ensure objects are above ground

        return spawnPos;
    }

    GameObject CreateEnemy(Vector3 position)
    {
        GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        enemy.transform.position = position;
        enemy.name = "TrainingEnemy";

        // Try to set tag, but don't fail if it doesn't exist
        try
        {
            enemy.tag = "Enemy";
        }
        catch (UnityException)
        {
            Debug.LogWarning("⚠️ 'Enemy' tag not found. Use 'SquadMate AI → Setup → Create Required Tags'");
        }

        // Visual styling
        Renderer renderer = enemy.GetComponent<Renderer>();
        renderer.material.color = Color.red;

        // Add enemy AI component
        EnemyAI enemyAI = enemy.AddComponent<EnemyAI>();
        enemyAI.health = enemyHealth;
        enemyAI.damage = enemyDamage;
        enemyAI.speed = enemySpeed;
        enemyAI.detectionRange = enemyDetectionRange;
        enemyAI.attackRange = enemyAttackRange;

        // Add collider for interactions
        enemy.GetComponent<Collider>().isTrigger = false;

        return enemy;
    }

    GameObject CreateWeapon(Vector3 position)
    {
        GameObject weapon = GameObject.CreatePrimitive(PrimitiveType.Cube);
        weapon.transform.position = position;
        weapon.transform.localScale = Vector3.one * 0.5f;

        // Random weapon type
        int weaponIndex = Random.Range(0, weaponTypes.Length);
        weapon.name = $"Weapon_{weaponTypes[weaponIndex]}";

        // Try to set tag, but don't fail if it doesn't exist
        try
        {
            weapon.tag = "Weapon";
        }
        catch (UnityException)
        {
            Debug.LogWarning("⚠️ 'Weapon' tag not found. Use 'SquadMate AI → Setup → Create Required Tags'");
        }

        // Visual styling
        Renderer renderer = weapon.GetComponent<Renderer>();
        renderer.material.color = Color.yellow;

        // Add weapon component
        WeaponPickup weaponPickup = weapon.AddComponent<WeaponPickup>();
        weaponPickup.weaponType = weaponTypes[weaponIndex];
        weaponPickup.damage = weaponDamage[weaponIndex];
        weaponPickup.range = weaponRange[weaponIndex];
        weaponPickup.fireRate = weaponFireRate[weaponIndex];

        // Make it a trigger for pickup
        weapon.GetComponent<Collider>().isTrigger = true;

        return weapon;
    }

    GameObject CreateMedkit(Vector3 position)
    {
        GameObject medkit = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        medkit.transform.position = position;
        medkit.transform.localScale = Vector3.one * 0.7f;
        medkit.name = "Medkit";

        // Try to set tag, but don't fail if it doesn't exist
        try
        {
            medkit.tag = "Medkit";
        }
        catch (UnityException)
        {
            Debug.LogWarning("⚠️ 'Medkit' tag not found. Use 'SquadMate AI → Setup → Create Required Tags'");
        }

        // Visual styling
        Renderer renderer = medkit.GetComponent<Renderer>();
        renderer.material.color = Color.green;

        // Add medkit component
        MedkitPickup medkitPickup = medkit.AddComponent<MedkitPickup>();
        medkitPickup.healAmount = medkitHealAmount;

        // Make it a trigger for pickup
        medkit.GetComponent<Collider>().isTrigger = true;

        return medkit;
    }

    void CleanupDestroyedObjects()
    {
        activeEnemies.RemoveAll(enemy => enemy == null);
        activeWeapons.RemoveAll(weapon => weapon == null);
        activeMedkits.RemoveAll(medkit => medkit == null);
    }

    void ClearAllSpawns()
    {
        foreach (GameObject enemy in activeEnemies)
        {
            if (enemy != null) DestroyImmediate(enemy);
        }
        foreach (GameObject weapon in activeWeapons)
        {
            if (weapon != null) DestroyImmediate(weapon);
        }
        foreach (GameObject medkit in activeMedkits)
        {
            if (medkit != null) DestroyImmediate(medkit);
        }

        activeEnemies.Clear();
        activeWeapons.Clear();
        activeMedkits.Clear();
    }

    void OnGUI()
    {
        if (!enableCombatTraining) return;

        // Combat training info panel (top-center)
        float panelWidth = 350f;
        float panelHeight = 140f;
        float x = (Screen.width - panelWidth) / 2f;
        float y = 15f;

        GUILayout.BeginArea(new Rect(x, y, panelWidth, panelHeight));
        GUILayout.BeginVertical("box");

        GUIStyle boldStyle = new GUIStyle(GUI.skin.label);
        boldStyle.fontStyle = FontStyle.Bold;
        GUILayout.Label($"⚔️ Combat Training: {currentScenario}", boldStyle);

        GUILayout.BeginHorizontal();
        GUILayout.Label($"👹 Enemies: {activeEnemies.Count}/{maxEnemies}");
        GUILayout.Label($"🔫 Weapons: {activeWeapons.Count}/{maxWeapons}");
        GUILayout.Label($"💊 Medkits: {activeMedkits.Count}/{maxMedkits}");
        GUILayout.EndHorizontal();

        GUILayout.Label($"🎯 Next Scenario: {(int)(scenarioSwitchInterval - (Time.time - lastScenarioSwitch)):F0}s");

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("🔥 Aggressive"))
        {
            currentScenario = TrainingScenario.Aggressive;
            ApplyScenarioSettings();
        }
        if (GUILayout.Button("🛡️ Defensive"))
        {
            currentScenario = TrainingScenario.Defensive;
            ApplyScenarioSettings();
        }
        if (GUILayout.Button("💀 Survival"))
        {
            currentScenario = TrainingScenario.Survival;
            ApplyScenarioSettings();
        }
        GUILayout.EndHorizontal();

        GUILayout.EndVertical();
        GUILayout.EndArea();
    }

    void OnDestroy()
    {
        ClearAllSpawns();
    }
}
