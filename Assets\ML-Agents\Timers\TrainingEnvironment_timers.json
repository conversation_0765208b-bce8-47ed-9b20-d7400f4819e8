{"count": 1, "self": 1826.6163199999999, "total": 1828.0159844999998, "children": {"InitializeActuators": {"count": 1, "self": 0.0029911, "total": 0.0029911, "children": null}, "InitializeSensors": {"count": 1, "self": 0.0019941, "total": 0.0019941, "children": null}, "AgentSendState": {"count": 297383, "self": 0.34541639999999996, "total": 0.34541639999999996, "children": null}, "DecideAction": {"count": 297383, "self": 0.9317019, "total": 0.9317019, "children": null}, "AgentAct": {"count": 297383, "self": 0.11747919999999999, "total": 0.11747919999999999, "children": null}}, "gauges": {}, "metadata": {"timer_format_version": "0.1.0", "start_time_seconds": "**********", "unity_version": "6000.1.6f1", "command_line_arguments": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Unity.exe -projectpath C:\\Squadmate -useHub -hubIPC -cloudEnvironment production -licensingIpc LicenseClient-BCE -hubSessionId 330a911c-6cd8-43b5-bc8b-1d81e1889f3b -accessToken 0IxbCtmOLhkTMZzEbbr0KiCJUARhYk4olFwLA2lr1NQ00cf", "communication_protocol_version": "1.5.0", "com.unity.ml-agents_version": "2.0.1", "scene_name": "TrainingEnvironment", "end_time_seconds": "**********"}}