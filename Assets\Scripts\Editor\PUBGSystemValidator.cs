using UnityEngine;
using UnityEditor;

/// <summary>
/// Validates that all PUBG systems are working correctly
/// </summary>
public class PUBGSystemValidator : EditorWindow
{
    [MenuItem("SquadMate AI/🧪 Validate PUBG Systems")]
    public static void ValidatePUBGSystems()
    {
        Debug.Log("🧪 Validating PUBG Systems...");
        
        bool allValid = true;
        
        // Test 1: Check if all required scripts exist
        allValid &= ValidateScriptFiles();
        
        // Test 2: Check if item database can be created
        allValid &= ValidateItemDatabase();
        
        // Test 3: Check if systems can be instantiated
        allValid &= ValidateSystemInstantiation();
        
        if (allValid)
        {
            Debug.Log("✅ All PUBG systems validated successfully!");
            Debug.Log("🚀 Ready for training setup!");
        }
        else
        {
            Debug.LogError("❌ Some PUBG systems failed validation. Check the logs above.");
        }
    }
    
    static bool ValidateScriptFiles()
    {
        Debug.Log("📁 Validating script files...");
        
        string[] requiredScripts = {
            "Assets/Scripts/Environment/PUBGItemSystem.cs",
            "Assets/Scripts/Environment/PUBGItemDatabase.cs", 
            "Assets/Scripts/Environment/PUBGLootPickup.cs",
            "Assets/Scripts/Environment/PUBGInventory.cs",
            "Assets/Scripts/Agents/SquadMateAgent.cs",
            "Assets/Scripts/Environment/EnemyAI.cs",
            "Assets/Scripts/Editor/PUBGTrainingSetup.cs"
        };
        
        bool allExist = true;
        foreach (string scriptPath in requiredScripts)
        {
            if (!System.IO.File.Exists(scriptPath))
            {
                Debug.LogError($"❌ Missing script: {scriptPath}");
                allExist = false;
            }
            else
            {
                Debug.Log($"✅ Found: {scriptPath}");
            }
        }
        
        return allExist;
    }
    
    static bool ValidateItemDatabase()
    {
        Debug.Log("📦 Validating PUBG Item Database...");
        
        try
        {
            // Create a test item database
            PUBGItemDatabase testDB = ScriptableObject.CreateInstance<PUBGItemDatabase>();
            testDB.InitializeDefaultItems();
            
            // Check if items were created
            if (testDB.weapons.Count > 0)
            {
                Debug.Log($"✅ Created {testDB.weapons.Count} weapons");
            }
            else
            {
                Debug.LogError("❌ No weapons created in database");
                return false;
            }
            
            if (testDB.healingItems.Count > 0)
            {
                Debug.Log($"✅ Created {testDB.healingItems.Count} healing items");
            }
            else
            {
                Debug.LogError("❌ No healing items created in database");
                return false;
            }
            
            if (testDB.armorItems.Count > 0)
            {
                Debug.Log($"✅ Created {testDB.armorItems.Count} armor items");
            }
            else
            {
                Debug.LogError("❌ No armor items created in database");
                return false;
            }
            
            // Test random item selection
            PUBGItem randomWeapon = testDB.GetRandomItem(PUBGItemType.Weapon);
            if (randomWeapon != null)
            {
                Debug.Log($"✅ Random weapon test: {randomWeapon.itemName}");
            }
            else
            {
                Debug.LogError("❌ Failed to get random weapon");
                return false;
            }
            
            // Clean up
            DestroyImmediate(testDB);
            
            return true;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Item database validation failed: {e.Message}");
            return false;
        }
    }
    
    static bool ValidateSystemInstantiation()
    {
        Debug.Log("🔧 Validating system instantiation...");
        
        try
        {
            // Create a temporary game object to test components
            GameObject testObj = new GameObject("PUBG_Test_Object");
            
            // Test PUBGInventory
            PUBGInventory inventory = testObj.AddComponent<PUBGInventory>();
            if (inventory != null)
            {
                Debug.Log("✅ PUBGInventory component created");
            }
            else
            {
                Debug.LogError("❌ Failed to create PUBGInventory");
                DestroyImmediate(testObj);
                return false;
            }
            
            // Test PUBGArmorSystem
            PUBGArmorSystem armorSystem = testObj.AddComponent<PUBGArmorSystem>();
            if (armorSystem != null)
            {
                Debug.Log("✅ PUBGArmorSystem component created");
            }
            else
            {
                Debug.LogError("❌ Failed to create PUBGArmorSystem");
                DestroyImmediate(testObj);
                return false;
            }
            
            // Test item creation
            PUBGItem testItem = new PUBGItem();
            testItem.itemName = "Test Item";
            testItem.itemType = PUBGItemType.Weapon;
            testItem.rarity = ItemRarity.Common;
            
            if (testItem.itemName == "Test Item")
            {
                Debug.Log("✅ PUBGItem creation test passed");
            }
            else
            {
                Debug.LogError("❌ PUBGItem creation test failed");
                DestroyImmediate(testObj);
                return false;
            }
            
            // Clean up
            DestroyImmediate(testObj);
            
            return true;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ System instantiation validation failed: {e.Message}");
            return false;
        }
    }
    
    [MenuItem("SquadMate AI/🔧 Quick Fix Common Issues")]
    public static void QuickFixCommonIssues()
    {
        Debug.Log("🔧 Running quick fixes for common issues...");
        
        // Fix 1: Ensure required tags exist
        CreateRequiredTags();
        
        // Fix 2: Create directories if they don't exist
        CreateRequiredDirectories();
        
        Debug.Log("✅ Quick fixes completed!");
    }
    
    static void CreateRequiredTags()
    {
        Debug.Log("🏷️ Creating required tags...");
        
        string[] requiredTags = { "Player", "Enemy", "Loot", "Cover" };
        
        foreach (string tag in requiredTags)
        {
            if (!TagExists(tag))
            {
                CreateTag(tag);
                Debug.Log($"✅ Created tag: {tag}");
            }
            else
            {
                Debug.Log($"✅ Tag exists: {tag}");
            }
        }
    }
    
    static bool TagExists(string tag)
    {
        for (int i = 0; i < UnityEditorInternal.InternalEditorUtility.tags.Length; i++)
        {
            if (UnityEditorInternal.InternalEditorUtility.tags[i] == tag)
                return true;
        }
        return false;
    }
    
    static void CreateTag(string tag)
    {
        SerializedObject tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
        SerializedProperty tagsProp = tagManager.FindProperty("tags");
        
        tagsProp.InsertArrayElementAtIndex(tagsProp.arraySize);
        SerializedProperty newTagProp = tagsProp.GetArrayElementAtIndex(tagsProp.arraySize - 1);
        newTagProp.stringValue = tag;
        
        tagManager.ApplyModifiedProperties();
    }
    
    static void CreateRequiredDirectories()
    {
        Debug.Log("📁 Creating required directories...");
        
        string[] directories = {
            "Assets/Prefabs",
            "Assets/Materials", 
            "Assets/Models",
            "Assets/Textures",
            "results",
            "logs",
            "models",
            "checkpoints"
        };
        
        foreach (string dir in directories)
        {
            if (!System.IO.Directory.Exists(dir))
            {
                System.IO.Directory.CreateDirectory(dir);
                Debug.Log($"✅ Created directory: {dir}");
            }
            else
            {
                Debug.Log($"✅ Directory exists: {dir}");
            }
        }
        
        AssetDatabase.Refresh();
    }
}
