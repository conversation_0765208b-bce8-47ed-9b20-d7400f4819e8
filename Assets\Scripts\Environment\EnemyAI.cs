using UnityEngine;
using System.Collections;

/// <summary>
/// Smart Enemy AI that provides realistic combat training for the SquadMate agent
/// </summary>
public class EnemyAI : MonoBehaviour
{
    [Header("🎯 Enemy Stats")]
    public float health = 100f;
    public float maxHealth = 100f;
    public float damage = 25f;
    public float speed = 3f;
    public float detectionRange = 8f;
    public float attackRange = 2f;
    public float attackCooldown = 1.5f;

    [Header("🧠 AI Behavior")]
    public float aggressionLevel = 0.7f;
    public float retreatHealthThreshold = 0.3f;
    public bool canFlank = true;
    public bool canTakeCover = true;

    [Header("🎯 Combat Targets")]
    public Transform[] potentialTargets;
    public Transform currentTarget;
    public float targetSwitchCooldown = 2f;
    private float lastTargetSwitch = 0f;

    [Header("🎮 AI States")]
    public EnemyState currentState = EnemyState.Patrolling;
    public Vector3 lastKnownTargetPosition;
    public float lostTargetTime = 0f;

    public enum EnemyState
    {
        Patrolling,
        Chasing,
        Attacking,
        TakingCover,
        Flanking,
        Retreating,
        Searching
    }

    // Internal state
    private Transform target;
    private Transform player;
    private SquadMateAgent squadMate;
    private Vector3 lastKnownPlayerPos;
    private Vector3 lastKnownSquadMatePos;
    private float lastAttackTime;
    private bool isRetreating = false;
    private Vector3 retreatPosition;
    private EnemyState currentState = EnemyState.Patrolling;

    // Visual feedback
    private Renderer enemyRenderer;
    private Color originalColor;

    public enum EnemyState
    {
        Patrolling,
        Hunting,
        Attacking,
        Retreating,
        Flanking,
        TakingCover
    }

    void Start()
    {
        maxHealth = health;
        player = FindObjectOfType<PlayerController>()?.transform;
        squadMate = FindObjectOfType<SquadMateAgent>();

        enemyRenderer = GetComponent<Renderer>();
        originalColor = enemyRenderer.material.color;

        // Start patrolling
        StartCoroutine(AIBehaviorLoop());

        Debug.Log($"👹 Enemy spawned with {health} HP, {damage} damage");
    }

    IEnumerator AIBehaviorLoop()
    {
        while (health > 0)
        {
            UpdateAIState();
            ExecuteCurrentState();
            yield return new WaitForSeconds(0.1f);
        }
    }

    void UpdateAIState()
    {
        // Find closest target (player or squadmate)
        float distToPlayer = player ? Vector3.Distance(transform.position, player.position) : float.MaxValue;
        float distToSquadMate = squadMate ? Vector3.Distance(transform.position, squadMate.transform.position) : float.MaxValue;

        // Choose target based on distance and threat level
        if (distToPlayer < distToSquadMate && distToPlayer < detectionRange)
        {
            target = player;
            lastKnownPlayerPos = player.position;
        }
        else if (distToSquadMate < detectionRange)
        {
            target = squadMate.transform;
            lastKnownSquadMatePos = squadMate.transform.position;
        }
        else
        {
            target = null;
        }

        // Determine state based on health and target
        float healthPercent = health / maxHealth;

        if (healthPercent < retreatHealthThreshold && !isRetreating)
        {
            currentState = EnemyState.Retreating;
            isRetreating = true;
            retreatPosition = GetRetreatPosition();
        }
        else if (target != null)
        {
            float distToTarget = Vector3.Distance(transform.position, target.position);

            if (distToTarget <= attackRange)
            {
                currentState = EnemyState.Attacking;
            }
            else if (distToTarget <= detectionRange)
            {
                // Decide between hunting, flanking, or taking cover
                if (canFlank && Random.value < 0.3f && aggressionLevel > 0.5f)
                {
                    currentState = EnemyState.Flanking;
                }
                else if (canTakeCover && healthPercent < 0.6f && Random.value < 0.4f)
                {
                    currentState = EnemyState.TakingCover;
                }
                else
                {
                    currentState = EnemyState.Hunting;
                }
            }
        }
        else
        {
            currentState = EnemyState.Patrolling;
            isRetreating = false;
        }

        // Update visual feedback
        UpdateVisualFeedback();
    }

    void ExecuteCurrentState()
    {
        switch (currentState)
        {
            case EnemyState.Patrolling:
                Patrol();
                break;
            case EnemyState.Hunting:
                Hunt();
                break;
            case EnemyState.Attacking:
                Attack();
                break;
            case EnemyState.Retreating:
                Retreat();
                break;
            case EnemyState.Flanking:
                Flank();
                break;
            case EnemyState.TakingCover:
                TakeCover();
                break;
        }
    }

    void Patrol()
    {
        // Simple patrol behavior - move randomly
        if (Random.value < 0.1f)
        {
            Vector3 randomDir = Random.insideUnitSphere;
            randomDir.y = 0;
            transform.position += randomDir.normalized * speed * 0.5f * Time.deltaTime;
        }
    }

    void Hunt()
    {
        if (target == null) return;

        // Move towards target with some randomness
        Vector3 direction = (target.position - transform.position).normalized;
        Vector3 randomOffset = Random.insideUnitSphere * 0.5f;
        randomOffset.y = 0;

        Vector3 moveDirection = (direction + randomOffset).normalized;
        transform.position += moveDirection * speed * Time.deltaTime;

        // Look at target
        transform.LookAt(new Vector3(target.position.x, transform.position.y, target.position.z));
    }

    void Attack()
    {
        if (target == null) return;

        // Look at target
        transform.LookAt(new Vector3(target.position.x, transform.position.y, target.position.z));

        // Attack if cooldown is ready
        if (Time.time - lastAttackTime > attackCooldown)
        {
            PerformAttack();
            lastAttackTime = Time.time;
        }

        // Move slightly closer if not in optimal range
        float distToTarget = Vector3.Distance(transform.position, target.position);
        if (distToTarget > attackRange * 0.8f)
        {
            Vector3 direction = (target.position - transform.position).normalized;
            transform.position += direction * speed * 0.5f * Time.deltaTime;
        }
    }

    void Retreat()
    {
        // Move away from target towards retreat position
        Vector3 direction = (retreatPosition - transform.position).normalized;
        transform.position += direction * speed * 1.2f * Time.deltaTime;

        // Stop retreating when reached retreat position or health recovered
        if (Vector3.Distance(transform.position, retreatPosition) < 2f || health > maxHealth * 0.6f)
        {
            isRetreating = false;
        }
    }

    void Flank()
    {
        if (target == null) return;

        // Try to move to the side of the target
        Vector3 toTarget = target.position - transform.position;
        Vector3 flankDirection = Vector3.Cross(toTarget, Vector3.up).normalized;

        // Randomly choose left or right flank
        if (Random.value < 0.5f) flankDirection = -flankDirection;

        Vector3 flankPosition = target.position + flankDirection * 5f;
        Vector3 direction = (flankPosition - transform.position).normalized;

        transform.position += direction * speed * Time.deltaTime;
        transform.LookAt(new Vector3(target.position.x, transform.position.y, target.position.z));
    }

    void TakeCover()
    {
        // Move away from target to create distance
        if (target == null) return;

        Vector3 awayFromTarget = (transform.position - target.position).normalized;
        transform.position += awayFromTarget * speed * 0.8f * Time.deltaTime;

        // Still face the target
        transform.LookAt(new Vector3(target.position.x, transform.position.y, target.position.z));
    }

    void PerformAttack()
    {
        if (target == null) return;

        Debug.Log($"👹 Enemy attacks {target.name} for {damage} damage!");

        // Visual attack effect
        StartCoroutine(AttackFlash());

        // Deal damage to target
        if (target.CompareTag("Player"))
        {
            PlayerController playerController = target.GetComponent<PlayerController>();
            if (playerController != null)
            {
                playerController.TakeDamage(damage);
            }
        }
        else if (target.GetComponent<SquadMateAgent>() != null)
        {
            SquadMateAgent agent = target.GetComponent<SquadMateAgent>();

            // Determine if it's a headshot (10% chance)
            bool isHeadshot = Random.value < 0.1f;
            float finalDamage = isHeadshot ? damage * 2f : damage;

            // Agent takes damage - this will trigger learning about combat
            agent.TakeDamage(finalDamage, isHeadshot);

            Debug.Log($"🎯 Enemy hit SquadMate for {finalDamage} damage{(isHeadshot ? " (HEADSHOT!)" : "")}");
        }
    }

    IEnumerator AttackFlash()
    {
        enemyRenderer.material.color = Color.white;
        yield return new WaitForSeconds(0.1f);
        enemyRenderer.material.color = originalColor;
    }

    Vector3 GetRetreatPosition()
    {
        // Find a position away from all threats
        Vector3 awayFromPlayer = player ? (transform.position - player.position).normalized : Vector3.zero;
        Vector3 awayFromSquadMate = squadMate ? (transform.position - squadMate.transform.position).normalized : Vector3.zero;

        Vector3 retreatDir = (awayFromPlayer + awayFromSquadMate).normalized;
        return transform.position + retreatDir * 8f;
    }

    void UpdateVisualFeedback()
    {
        // Change color based on state
        switch (currentState)
        {
            case EnemyState.Patrolling:
                enemyRenderer.material.color = Color.red;
                break;
            case EnemyState.Hunting:
                enemyRenderer.material.color = Color.yellow;
                break;
            case EnemyState.Attacking:
                enemyRenderer.material.color = Color.magenta;
                break;
            case EnemyState.Retreating:
                enemyRenderer.material.color = Color.blue;
                break;
            case EnemyState.Flanking:
                enemyRenderer.material.color = Color.cyan;
                break;
            case EnemyState.TakingCover:
                enemyRenderer.material.color = Color.gray;
                break;
        }

        // Scale based on health
        float healthPercent = health / maxHealth;
        transform.localScale = Vector3.one * (0.8f + healthPercent * 0.4f);
    }

    public void TakeDamage(float damageAmount)
    {
        health -= damageAmount;
        Debug.Log($"👹 Enemy takes {damageAmount} damage! Health: {health}/{maxHealth}");

        if (health <= 0)
        {
            Die();
        }
        else
        {
            // Flash red when taking damage
            StartCoroutine(DamageFlash());
        }
    }

    IEnumerator DamageFlash()
    {
        Color originalColor = enemyRenderer.material.color;
        enemyRenderer.material.color = Color.red;
        yield return new WaitForSeconds(0.2f);
        enemyRenderer.material.color = originalColor;
    }

    void Die()
    {
        Debug.Log("💀 Enemy defeated!");

        // Reward the agent for enemy elimination
        if (squadMate != null)
        {
            squadMate.AddReward(0.5f); // Good reward for eliminating threat
        }

        // Death effect
        StartCoroutine(DeathEffect());
    }

    IEnumerator DeathEffect()
    {
        // Shrink and fade
        float timer = 0f;
        Vector3 originalScale = transform.localScale;
        Color originalColor = enemyRenderer.material.color;

        while (timer < 1f)
        {
            timer += Time.deltaTime * 2f;
            transform.localScale = Vector3.Lerp(originalScale, Vector3.zero, timer);

            Color newColor = originalColor;
            newColor.a = 1f - timer;
            enemyRenderer.material.color = newColor;

            yield return null;
        }

        Destroy(gameObject);
    }

    void OnDrawGizmosSelected()
    {
        // Draw detection range
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, detectionRange);

        // Draw attack range
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, attackRange);

        // Draw line to target
        if (target != null)
        {
            Gizmos.color = Color.white;
            Gizmos.DrawLine(transform.position, target.position);
        }
    }
}
