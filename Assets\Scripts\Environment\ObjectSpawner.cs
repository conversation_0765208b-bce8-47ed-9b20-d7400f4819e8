using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ObjectSpawner : MonoBehaviour
{
    [Header("Spawning Settings")]
    public bool autoSpawn = true;
    public float spawnInterval = 30f;
    public int maxObjectsPerType = 10;

    [Header("Spawn Probabilities")]
    [Range(0f, 1f)]
    public float medkitSpawnChance = 0.3f;
    [Range(0f, 1f)]
    public float weaponSpawnChance = 0.2f;
    [Range(0f, 1f)]
    public float enemySpawnChance = 0.1f;

    [Header("Prefab References")]
    public GameObject[] medkitPrefabs;
    public GameObject[] weaponPrefabs;
    public GameObject[] enemyPrefabs;

    [Header("Spawn Areas")]
    public Transform[] spawnZones;
    public LayerMask groundLayer = 1;

    private GameEnvironment environment;
    private float lastSpawnTime;

    void Start()
    {
        environment = GetComponent<GameEnvironment>();
        if (environment == null)
        {
            environment = FindObjectOfType<GameEnvironment>();
        }

        lastSpawnTime = Time.time;
    }

    void Update()
    {
        if (autoSpawn && Time.time - lastSpawnTime > spawnInterval)
        {
            TrySpawnObjects();
            lastSpawnTime = Time.time;
        }
    }

    public void TrySpawnObjects()
    {
        // Try to spawn medkits
        if (Random.Range(0f, 1f) < medkitSpawnChance)
        {
            SpawnMedkit();
        }

        // Try to spawn weapons
        if (Random.Range(0f, 1f) < weaponSpawnChance)
        {
            SpawnWeapon();
        }

        // Try to spawn enemies
        if (Random.Range(0f, 1f) < enemySpawnChance)
        {
            SpawnEnemy();
        }
    }

    public GameObject SpawnMedkit()
    {
        if (medkitPrefabs == null || medkitPrefabs.Length == 0) return null;

        // Check current count
        if (environment != null && environment.GetMedkits().Length >= maxObjectsPerType)
        {
            return null;
        }

        Vector3 spawnPosition = GetRandomSpawnPosition();
        if (spawnPosition == Vector3.zero) return null;

        GameObject prefab = medkitPrefabs[Random.Range(0, medkitPrefabs.Length)];
        GameObject medkit = Instantiate(prefab, spawnPosition, Quaternion.identity);

        // Add pickup behavior
        MedkitPickup pickup = medkit.GetComponent<MedkitPickup>();
        if (pickup == null)
        {
            pickup = medkit.AddComponent<MedkitPickup>();
        }

        Debug.Log($"Spawned medkit at {spawnPosition}");
        return medkit;
    }

    public GameObject SpawnWeapon()
    {
        if (weaponPrefabs == null || weaponPrefabs.Length == 0) return null;

        // Check current count
        if (environment != null && environment.GetWeapons().Length >= maxObjectsPerType)
        {
            return null;
        }

        Vector3 spawnPosition = GetRandomSpawnPosition();
        if (spawnPosition == Vector3.zero) return null;

        GameObject prefab = weaponPrefabs[Random.Range(0, weaponPrefabs.Length)];
        GameObject weapon = Instantiate(prefab, spawnPosition, Quaternion.identity);

        // Add pickup behavior
        WeaponPickup pickup = weapon.GetComponent<WeaponPickup>();
        if (pickup == null)
        {
            pickup = weapon.AddComponent<WeaponPickup>();
        }

        Debug.Log($"Spawned weapon at {spawnPosition}");
        return weapon;
    }

    public GameObject SpawnEnemy()
    {
        if (enemyPrefabs == null || enemyPrefabs.Length == 0) return null;

        // Check current count
        if (environment != null && environment.GetEnemies().Length >= maxObjectsPerType)
        {
            return null;
        }

        Vector3 spawnPosition = GetRandomSpawnPosition();
        if (spawnPosition == Vector3.zero) return null;

        GameObject prefab = enemyPrefabs[Random.Range(0, enemyPrefabs.Length)];
        GameObject enemy = Instantiate(prefab, spawnPosition, Quaternion.identity);

        // Configure enemy
        EnemyController enemyController = enemy.GetComponent<EnemyController>();
        if (enemyController != null)
        {
            enemyController.environment = environment;
        }

        Debug.Log($"Spawned enemy at {spawnPosition}");
        return enemy;
    }

    private Vector3 GetRandomSpawnPosition()
    {
        Vector3 spawnPosition = Vector3.zero;
        int attempts = 0;
        int maxAttempts = 20;

        while (attempts < maxAttempts)
        {
            Vector3 candidatePosition;

            // Use spawn zones if available
            if (spawnZones != null && spawnZones.Length > 0)
            {
                Transform randomZone = spawnZones[Random.Range(0, spawnZones.Length)];
                candidatePosition = GetRandomPositionInZone(randomZone);
            }
            else
            {
                // Use environment bounds
                candidatePosition = environment != null ?
                    environment.GetRandomPosition() :
                    GetRandomPositionInArea(Vector3.zero, 20f);
            }

            // Check if position is valid
            if (IsValidSpawnPosition(candidatePosition))
            {
                spawnPosition = candidatePosition;
                break;
            }

            attempts++;
        }

        return spawnPosition;
    }

    private Vector3 GetRandomPositionInZone(Transform zone)
    {
        if (zone == null) return Vector3.zero;

        // Assume zone has a collider that defines the spawn area
        Collider zoneCollider = zone.GetComponent<Collider>();
        if (zoneCollider != null)
        {
            Bounds bounds = zoneCollider.bounds;
            Vector3 randomPoint = new Vector3(
                Random.Range(bounds.min.x, bounds.max.x),
                bounds.center.y,
                Random.Range(bounds.min.z, bounds.max.z)
            );

            // Raycast to find ground
            return FindGroundPosition(randomPoint);
        }
        else
        {
            // Use zone position with random offset
            Vector3 randomOffset = Random.insideUnitSphere * 5f;
            randomOffset.y = 0;
            return FindGroundPosition(zone.position + randomOffset);
        }
    }

    private Vector3 GetRandomPositionInArea(Vector3 center, float radius)
    {
        Vector3 randomDirection = Random.insideUnitSphere * radius;
        randomDirection.y = 0;
        Vector3 candidatePosition = center + randomDirection;

        return FindGroundPosition(candidatePosition);
    }

    private Vector3 FindGroundPosition(Vector3 position)
    {
        RaycastHit hit;
        Vector3 rayStart = new Vector3(position.x, position.y + 50f, position.z);

        if (Physics.Raycast(rayStart, Vector3.down, out hit, 100f, groundLayer))
        {
            return hit.point + Vector3.up * 0.1f;
        }
        else
        {
            return new Vector3(position.x, 0f, position.z);
        }
    }

    private bool IsValidSpawnPosition(Vector3 position)
    {
        // Check if position is too close to player or squadmate
        if (environment != null)
        {
            if (environment.player != null)
            {
                float distanceToPlayer = Vector3.Distance(position, environment.player.position);
                if (distanceToPlayer < 5f) return false;
            }

            if (environment.squadMate != null)
            {
                float distanceToSquadmate = Vector3.Distance(position, environment.squadMate.transform.position);
                if (distanceToSquadmate < 5f) return false;
            }
        }

        // Check if position is not inside other objects
        Collider[] overlapping = Physics.OverlapSphere(position, 1f);
        foreach (Collider col in overlapping)
        {
            if (col.gameObject.layer == LayerMask.NameToLayer("Obstacle") ||
                col.gameObject.layer == LayerMask.NameToLayer("Wall"))
            {
                return false;
            }
        }

        return true;
    }

    public void SpawnObjectAtPosition(GameObject prefab, Vector3 position)
    {
        if (prefab != null)
        {
            Instantiate(prefab, position, Quaternion.identity);
        }
    }

    public void ClearAllSpawnedObjects()
    {
        // Clear medkits
        GameObject[] medkits = GameObject.FindGameObjectsWithTag("Medkit");
        foreach (GameObject medkit in medkits)
        {
            DestroyImmediate(medkit);
        }

        // Clear weapons
        GameObject[] weapons = GameObject.FindGameObjectsWithTag("Weapon");
        foreach (GameObject weapon in weapons)
        {
            DestroyImmediate(weapon);
        }

        // Clear enemies
        GameObject[] enemies = GameObject.FindGameObjectsWithTag("Enemy");
        foreach (GameObject enemy in enemies)
        {
            DestroyImmediate(enemy);
        }
    }

    void OnDrawGizmosSelected()
    {
        // Draw spawn zones
        if (spawnZones != null)
        {
            Gizmos.color = Color.green;
            foreach (Transform zone in spawnZones)
            {
                if (zone != null)
                {
                    Collider zoneCollider = zone.GetComponent<Collider>();
                    if (zoneCollider != null)
                    {
                        Gizmos.DrawWireCube(zoneCollider.bounds.center, zoneCollider.bounds.size);
                    }
                    else
                    {
                        Gizmos.DrawWireSphere(zone.position, 5f);
                    }
                }
            }
        }
    }
}

// Note: Advanced pickup components are now in separate files:
// - WeaponPickup.cs - Advanced weapon system with multiple weapon types
// - MedkitPickup.cs - Advanced healing system with health management
