using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// PUBG Loot Pickup Component - Handles item pickup logic and AI rewards
/// </summary>
public class PUBGLootPickup : MonoBehaviour
{
    [Header("📦 Item Data")]
    public PUBGItem itemData;
    public PUBGItemSystem itemSystem;
    
    [Header("🎯 Pickup Settings")]
    public float pickupRange = 2f;
    public float pickupTime = 0.5f;
    public bool autoPickup = true;
    
    [Header("🎨 Visual Effects")]
    public GameObject pickupEffect;
    public AudioClip pickupSound;
    
    private bool isBeingPickedUp = false;
    private Coroutine pickupCoroutine;
    
    void Start()
    {
        // Add glow effect based on rarity
        AddRarityGlow();
    }
    
    void AddRarityGlow()
    {
        if (itemData == null) return;
        
        // Create a simple glow effect
        GameObject glowObject = new GameObject("Glow");
        glowObject.transform.SetParent(transform);
        glowObject.transform.localPosition = Vector3.zero;
        glowObject.transform.localScale = Vector3.one * 1.2f;
        
        MeshRenderer glowRenderer = glowObject.AddComponent<MeshRenderer>();
        MeshFilter glowFilter = glowObject.AddComponent<MeshFilter>();
        
        // Use same mesh as parent
        MeshFilter parentMesh = GetComponent<MeshFilter>();
        if (parentMesh != null)
        {
            glowFilter.mesh = parentMesh.mesh;
        }
        
        // Create glow material
        Material glowMaterial = new Material(Shader.Find("Standard"));
        glowMaterial.SetFloat("_Mode", 3); // Transparent mode
        glowMaterial.color = GetRarityColor() * 0.3f;
        glowMaterial.SetFloat("_Metallic", 0f);
        glowMaterial.SetFloat("_Smoothness", 1f);
        
        glowRenderer.material = glowMaterial;
    }
    
    Color GetRarityColor()
    {
        switch (itemData.rarity)
        {
            case ItemRarity.Common: return Color.white;
            case ItemRarity.Uncommon: return Color.green;
            case ItemRarity.Rare: return Color.blue;
            case ItemRarity.Epic: return Color.magenta;
            case ItemRarity.Legendary: return Color.yellow;
            default: return Color.white;
        }
    }
    
    void OnTriggerEnter(Collider other)
    {
        if (isBeingPickedUp) return;
        
        // Check if it's a player or AI agent
        if (other.CompareTag("Player") || other.GetComponent<SquadMateAgent>() != null)
        {
            if (autoPickup)
            {
                StartPickup(other.gameObject);
            }
        }
    }
    
    void OnTriggerExit(Collider other)
    {
        if (pickupCoroutine != null && (other.CompareTag("Player") || other.GetComponent<SquadMateAgent>() != null))
        {
            StopCoroutine(pickupCoroutine);
            pickupCoroutine = null;
            isBeingPickedUp = false;
        }
    }
    
    public void StartPickup(GameObject picker)
    {
        if (isBeingPickedUp) return;
        
        pickupCoroutine = StartCoroutine(PickupProcess(picker));
    }
    
    IEnumerator PickupProcess(GameObject picker)
    {
        isBeingPickedUp = true;
        
        // Wait for pickup time
        yield return new WaitForSeconds(pickupTime);
        
        // Check if picker is still in range
        if (Vector3.Distance(transform.position, picker.transform.position) <= pickupRange)
        {
            PerformPickup(picker);
        }
        else
        {
            isBeingPickedUp = false;
        }
    }
    
    void PerformPickup(GameObject picker)
    {
        // Get or create inventory component
        PUBGInventory inventory = picker.GetComponent<PUBGInventory>();
        if (inventory == null)
        {
            inventory = picker.AddComponent<PUBGInventory>();
        }
        
        // Try to add item to inventory
        bool success = inventory.TryAddItem(itemData);
        
        if (success)
        {
            // Reward AI agent for smart pickup
            SquadMateAgent agent = picker.GetComponent<SquadMateAgent>();
            if (agent != null)
            {
                float reward = CalculatePickupReward(agent);
                agent.AddReward(reward);
                
                Debug.Log($"🎯 Agent rewarded {reward:F2} for picking up {itemData.itemName}");
            }
            
            // Apply item effects
            ApplyItemEffects(picker);
            
            // Visual and audio feedback
            PlayPickupEffects();
            
            // Remove from item system
            if (itemSystem != null)
            {
                itemSystem.RemoveLootItem(gameObject);
            }
            
            // Destroy the pickup
            Destroy(gameObject);
        }
        else
        {
            // Inventory full - stop pickup
            isBeingPickedUp = false;
            Debug.Log($"❌ {picker.name} inventory full - cannot pickup {itemData.itemName}");
        }
    }
    
    float CalculatePickupReward(SquadMateAgent agent)
    {
        float baseReward = 0.1f;
        float rarityBonus = (int)itemData.rarity * 0.05f;
        float situationalBonus = 0f;
        
        // Situational bonuses based on agent needs
        switch (itemData.itemType)
        {
            case PUBGItemType.Weapon:
                if (!agent.hasWeapon)
                {
                    situationalBonus = 0.5f; // High reward for first weapon
                }
                else
                {
                    situationalBonus = 0.1f; // Small reward for weapon upgrade
                }
                break;
                
            case PUBGItemType.Healing:
                float healthPercent = agent.currentHealth / agent.maxHealth;
                situationalBonus = (1f - healthPercent) * 0.4f; // More reward when low health
                break;
                
            case PUBGItemType.Armor:
                situationalBonus = 0.2f; // Always good to have armor
                break;
                
            case PUBGItemType.Backpack:
                situationalBonus = 0.15f; // Inventory space is valuable
                break;
                
            case PUBGItemType.Attachment:
                if (agent.hasWeapon)
                {
                    situationalBonus = 0.1f; // Good if we have a weapon
                }
                break;
                
            case PUBGItemType.Throwable:
                situationalBonus = 0.08f; // Tactical utility
                break;
        }
        
        return baseReward + rarityBonus + situationalBonus;
    }
    
    void ApplyItemEffects(GameObject picker)
    {
        switch (itemData.itemType)
        {
            case PUBGItemType.Weapon:
                ApplyWeaponEffects(picker);
                break;
                
            case PUBGItemType.Healing:
                if (itemData.isConsumable)
                {
                    ApplyHealingEffects(picker);
                }
                break;
                
            case PUBGItemType.Armor:
                ApplyArmorEffects(picker);
                break;
                
            case PUBGItemType.Backpack:
                ApplyBackpackEffects(picker);
                break;
        }
    }
    
    void ApplyWeaponEffects(GameObject picker)
    {
        WeaponSystem weaponSystem = picker.GetComponent<WeaponSystem>();
        if (weaponSystem == null)
        {
            weaponSystem = picker.AddComponent<WeaponSystem>();
        }
        
        weaponSystem.EquipWeapon(
            itemData.itemName,
            itemData.damage,
            itemData.range,
            itemData.fireRate,
            itemData.ammoCapacity
        );
        
        // Update agent weapon status
        SquadMateAgent agent = picker.GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            agent.hasWeapon = true;
        }
    }
    
    void ApplyHealingEffects(GameObject picker)
    {
        HealthSystem healthSystem = picker.GetComponent<HealthSystem>();
        if (healthSystem == null)
        {
            healthSystem = picker.AddComponent<HealthSystem>();
        }
        
        if (itemData.isBoostItem)
        {
            // Apply boost effect (energy drinks, painkillers)
            StartCoroutine(ApplyBoostEffect(healthSystem));
        }
        else
        {
            // Direct healing (bandages, first aid, med kit)
            float maxHeal = healthSystem.maxHealth * itemData.maxHealthPercent;
            float actualHeal = Mathf.Min(itemData.healAmount, maxHeal - healthSystem.currentHealth);
            
            if (actualHeal > 0)
            {
                healthSystem.Heal(actualHeal);
            }
        }
    }
    
    IEnumerator ApplyBoostEffect(HealthSystem healthSystem)
    {
        float boostDuration = 30f; // 30 seconds of boost
        float healPerSecond = itemData.healAmount / boostDuration;
        
        for (float t = 0; t < boostDuration; t += Time.deltaTime)
        {
            if (healthSystem != null && healthSystem.currentHealth < healthSystem.maxHealth)
            {
                healthSystem.Heal(healPerSecond * Time.deltaTime);
            }
            yield return null;
        }
    }
    
    void ApplyArmorEffects(GameObject picker)
    {
        PUBGArmorSystem armorSystem = picker.GetComponent<PUBGArmorSystem>();
        if (armorSystem == null)
        {
            armorSystem = picker.AddComponent<PUBGArmorSystem>();
        }
        
        if (itemData.isHelmet)
        {
            armorSystem.EquipHelmet(itemData);
        }
        else if (itemData.isVest)
        {
            armorSystem.EquipVest(itemData);
        }
    }
    
    void ApplyBackpackEffects(GameObject picker)
    {
        PUBGInventory inventory = picker.GetComponent<PUBGInventory>();
        if (inventory != null)
        {
            inventory.UpgradeBackpack(itemData.capacityBonus);
        }
    }
    
    void PlayPickupEffects()
    {
        // Visual effect
        if (pickupEffect != null)
        {
            Instantiate(pickupEffect, transform.position, Quaternion.identity);
        }
        
        // Audio effect
        if (pickupSound != null)
        {
            AudioSource.PlayClipAtPoint(pickupSound, transform.position);
        }
        
        // Simple particle effect
        CreatePickupParticles();
    }
    
    void CreatePickupParticles()
    {
        GameObject particles = new GameObject("PickupParticles");
        particles.transform.position = transform.position;
        
        ParticleSystem ps = particles.AddComponent<ParticleSystem>();
        var main = ps.main;
        main.startColor = GetRarityColor();
        main.startLifetime = 1f;
        main.startSpeed = 5f;
        main.maxParticles = 20;
        
        var emission = ps.emission;
        emission.SetBursts(new ParticleSystem.Burst[]
        {
            new ParticleSystem.Burst(0f, 20)
        });
        
        var shape = ps.shape;
        shape.shapeType = ParticleSystemShapeType.Sphere;
        shape.radius = 0.5f;
        
        // Destroy particles after 2 seconds
        Destroy(particles, 2f);
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw pickup range
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, pickupRange);
        
        // Draw item info
        if (itemData != null)
        {
            Gizmos.color = GetRarityColor();
            Gizmos.DrawWireCube(transform.position + Vector3.up * 2f, Vector3.one * 0.5f);
        }
    }
}
